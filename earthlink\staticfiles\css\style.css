@import url("https://fonts.googleapis.com/css2?family=Reddit+Sans:ital,wght@0,200..900;1,200..900&display=swap");

body,
html {
  margin: 0;
  padding: 0;
  font-family: "Reddit Sans";
  box-sizing: border-box;
}

.all-main h1,
.all-main h2,
.all-main h3,
.all-main h4,
.all-main h5,
.all-main h6,
.all-main p,
.all-main ul,
.all-main ol,
.all-main li {
  margin: 0px;
  padding: 0px;
}

.all-main a {
  text-decoration: none;
}
.mw-all-big-sections-space {
  padding: 3rem 1.5rem;
}

.all-main li {
  list-style: none;
}

/* Banner */
.el-common-padding {
  padding: 4.31rem 1.5rem;
}
.el-fiber-net .el-banner-yellow-bg.el-common-width a {
  margin-bottom: 0;
}

.el-common-width {
  max-width: 77rem;
  margin: 0 auto;
  padding: 0 1.5rem;
}
.new-banner-bg-image {
  position: relative;
}
.el-fiber-net {
  background: url(/staticfiles/images/young-girl-bg-image.webp);
  position: relative;
  background-repeat: no-repeat;
  background-position: right;
  background-size: contain;
  margin-top: 5.9rem;
}

.el-fiber-net::after {
  position: absolute;
  z-index: 1;
  clip-path: polygon(100% 0%, 75% 50%, 100% 100%, 0 100%, 0% 50%, 0 0);
  background-color: #ff8700;
  content: "";
  bottom: 0;
  left: 0;
  height: 100%;
  width: 60%;
}

.el-fiber-net::before {
  position: absolute;
  z-index: 1;
  clip-path: polygon(100% 0%, 75% 50%, 100% 100%, 0 100%, 0% 50%, 0 0);
  background-color: #0077a4;
  content: "";
  bottom: 0;
  left: 0;
  height: 100%;
  right: unset;
  top: 0;
  width: 61%;
}

.el-fiber-net .el-banner-yellow-bg {
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-fiber-net .el-banner-yellow-bg .el-your-way {
  width: 60%;
}

.el-fiber-net .el-banner-yellow-bg .ban-el-right {
  width: 40%;
}

.set-al-icon img {
  width: unset;
  height: 30px;
}

.el-fiber-net .el-your-way {
  position: relative;
  z-index: 2;
  /* Ensure the text is above the clip-path background */
}

.el-fiber-net .el-your-way h2 {
  font-size: 1.2rem;
  color: #fff;
  line-height: 2rem;
  font-weight: 600;
}
.el-fiber-net .el-your-way p {
  font-size: 0.9rem;
  max-width: 30rem;
  color: #fff;
  line-height: 1.4rem;
  margin-bottom: 1rem;
}
.el-fiber-net .el-your-way h1 {
  font-size: 2.5rem;
  color: #fff;
  line-height: 3rem;
  font-weight: 700;
  padding-bottom: 1rem;
  max-width: 30rem;
}

.el-fiber-net .el-your-way .el-no-caps li {
  display: flex;
  align-items: center;
  color: #fff;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 400;
}

.el-fiber-net .el-your-way .el-no-caps {
  padding-bottom: 1.5rem;
}

.el-fiber-net .el-your-way a,
.el-tempor .el-plans .el-basic-plan a,
.wl-consectetur .video-learn-more a,
.ctaa-buttons {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  gap: 0.8rem;
  background: #0077a4;
  padding: 0.8rem;
  margin-bottom: 4rem;
  width: 10rem;
  justify-content: center;
}
.ctaa-buttons {
  margin: 4rem auto 0;
  width: 8.7rem;
  border-radius: 30px;
  padding: 0.8rem 0.8rem;
  font-size: 1.2rem;
}
.ctaa-buttons img {
  height: 20px;
}
.all-cards-section .ctaa-buttons img {
  width: 20px;
}
.all-cards-section .ctaa-buttons {
  margin: 1.2rem 0 0;
}
.el-fiber-net .el-your-way a img,
.el-tempor .el-plans .el-basic-plan a img,
.video-learn-more img {
  height: 20px;
}

.el-fiber-net .el-your-way h2 {
  font-size: 1rem;
  font-weight: 500;
  color: #fff;
  max-width: 25rem;
}

/* Section 01 Best Value Realy Fast */

/* .el-tempor .el-top-content {
  text-align: center;
}

.el-tempor .el-top-content span,
.el-tempor .el-plans .el-basic-plan h3 {
  color: #b2b2b2;
  font-size: 1.5rem;
  font-weight: 500;
}

.el-tempor .el-top-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 2rem;
  padding: 1rem 0 0.8rem;
}

.el-tempor .el-top-content p {
  color: #b2b2b2;
  font-size: 1rem;
  font-weight: 400;
  max-width: 45rem;
  margin: 0 auto;
}

.el-tempor .el-plans {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 3rem;
  column-gap: 2rem;
}

.el-tempor .el-plans .el-basic-plan {
  background: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  padding: 1.5rem;
  border-radius: 5px;
  width: 27%;
}

/* .el-tempor .el-plans .el-basic-plan:hover {
    background: #ff8700;
    opacity: 0.3;
}

.el-tempor .el-plans .el-basic-plan:hover {
    opacity: 0.6;
    background-image: url(/staticfiles/images/al-cards-hover-bg.webp);
    background-size: cover;
    background-repeat: no-repeat;
} 

.el-tempor .el-plans .el-basic-plan {
  position: relative;
}

.el-tempor .el-plans .el-basic-plan:hover {
  background-color: #ff8700;
  opacity: 0.8;
}

.el-tempor .el-plans .el-basic-plan:hover::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url(/staticfiles/images/al-cards-hover-bg.webp);
  background-size: cover;
  background-repeat: no-repeat;
  opacity: 0.2;
  z-index: -1;
}

.el-tempor .el-plans .el-basic-plan h3,
.el-tempor .el-plans .el-basic-plan h5 {
  color: #ff8700;
  padding-bottom: 0.8rem;
}

.el-tempor .el-plans .el-basic-plan h4 {
  padding-bottom: 0.8rem;
  font-size: 1rem;
  font-weight: 700;
}

.el-tempor .el-plans .el-basic-plan h5 {
  padding-bottom: 0.8rem;
  font-size: 2rem;
  font-weight: 600;
}

.el-tempor .el-plans .el-basic-plan li {
  padding-bottom: 0.8rem;
  font-size: 0.8rem;
  font-weight: 500;
  color: #777777;
  line-height: 1.5rem;
}

.el-tempor .el-plans .el-basic-plan:hover h3,
.el-tempor .el-plans .el-basic-plan:hover h4,
.el-tempor .el-plans .el-basic-plan:hover h5,
.el-tempor .el-plans .el-basic-plan:hover li {
  color: #fff;
}

.el-tempor .el-plans .el-basic-plan a,
.el-fiber-near-me .video-learn-more a {
  margin-bottom: 0;
  padding: 0.8rem 0;
  column-gap: 0.8rem;
  font-size: 1rem;
  width: 100%;
  margin: 0 auto;
}

.el-basic-plan .el-gift-cards {
  height: 12rem;
}

.el-basic-plan .el-gift-cards li {
  list-style: inside;
}

.el-tempor .el-plans .el-basic-plan .el-gift-cards li::marker {
  color: #0077a4;
} */
.card {
  max-width: 75rem;
  margin: 0 auto;
  border-radius: 6px;
  padding: 0rem 4rem;
  display: flex; /* Flexbox ka istemal */
  flex-direction: column; /* Column direction mein items arrange karna */
  align-items: center; /* Horizontal axis mein center align karna */
}

.title {
  padding-bottom: 0.5rem;
  font-size: 1.8rem;
}

.line {
  width: 20%;
  height: 5px;
  background-color: #ff8700;
  border: transparent;
  margin-bottom: 4rem;
}

.steps {
  display: flex;
  justify-content: center;
  width: 100%;
  flex-wrap: wrap;
  row-gap: 3.8rem;
  column-gap: 3rem;
}
.step {
  width: 30%; /* Step ka width set karna */
}

.step__title {
  position: relative;
  background-color: #0077a4;
  height: 40px;
  width: 100%; /* Title ka width full lena */
  display: flex; /* Flexbox ka istemal */
  justify-content: center; /* Items ko horizontally center karna */
  align-items: center; /* Items ko vertically center karna */
  color: #fff;
}

.step__title:before,
.step__title:after {
  content: "";
  position: absolute;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
}

.step__title:before {
  border-left: 20px solid #fff;
  left: 0;
}

.step__title:after {
  border-left: 20px solid #0077a4;
  right: -20px;
}

.step__info {
  padding: 0rem 0.5rem;
}
section.el-really-fast.el-common-padding .step h2 {
  font-size: 1.5rem;
  font-weight: 500;
  padding-bottom: 0.5rem;
  padding-top: 1rem;
}
section.el-really-fast.el-common-padding .step p {
  line-height: 1.6rem;
}
/* customer-reviews */
.clients-testimonial {
  padding: 3rem 0px;
  background: #a8a7a70d;
}
.clients-testimonial .container-reviews {
  display: flex;
  justify-content: center;
  padding: 3rem 1.25rem 1.25rem;
  max-width: 74.375rem;
  margin: 0 auto;
}

.clients-testimonial .card-earthlnk {
  flex: 0 0 calc(43.33% - 1.25rem);
  border-radius: 0.5rem;
  padding: 2.65rem 1.25rem;
  margin-right: 1.25rem;
  text-align: center;
  box-shadow: 0 0.25rem 0.375rem rgba(0, 0, 0, 0.1);
  border: 1px solid #00000012;
  background-color: #fff;
}

.clients-testimonial .card-earthlnk:last-child {
  margin-right: 0;
}

.struggling-to-bill-heading h2 {
  font-size: 2rem;
  margin-bottom: 0.625rem;
  text-align: center;
}

.clients-testimonial .card-earthlnk p {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin-top: 0.75rem;
  margin-bottom: 1rem;
  max-width: 26rem;
  margin: 0.75rem auto 1rem;
}
/* Section 02 About Us */

.wl-consectetur .el-tempor {
  display: flex;
  align-items: start;
  justify-content: center;
  background: #f1f1f1;
  padding: 3rem 1.5rem;
  column-gap: 3rem;
}

.wl-consectetur .el-tempor .el-reliable {
  width: 33%;
}

.wl-consectetur .el-tempor .el-reliable img {
  width: 100%;
}

.wl-consectetur .el-tempor .el-top-content {
  width: 60%;
  text-align: left;
}

.wl-consectetur .el-lempor {
  display: flex;
  align-items: start;
  justify-content: center;
  column-gap: 1rem;
  padding-top: 1.8rem;
}

.wl-consectetur .el-lempor .el-video-content {
  width: 99%;
}

.wl-consectetur .el-lempor .el-about-us-video {
  width: 29%;
}

.wl-consectetur .el-lempor .el-about-us-video img {
  width: 100%;
}

.wl-consectetur .el-top-content h2 {
  max-width: 40rem;
  line-height: 3rem;
}

.wl-consectetur .el-top-content label {
  color: #ff8700;
}

.wl-consectetur .el-top-content p {
  max-width: unset;
}

.wl-consectetur .video-learn-more {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding-top: 2rem;
}

.wl-consectetur .video-learn-more a {
  margin-bottom: 0;
  align-items: center;
  justify-content: center;
}

.wl-consectetur .el-video-content.set-al-icon img {
  margin: 0.5rem 0;
  height: 40px;
}

/* Section 03 Fiber Connection */

.el-clients .el-satalite-connection .el-year-fiber img {
  width: unset;
  height: 45px;
  filter: brightness(0) invert(1);
}

.el-clients .el-satalite-connection .el-year-fiber.icon-new-set img {
  height: 60px;
  filter: brightness(0) invert(1);
}

.el-clients .el-satalite-connection {
  background: #ff8700;
  max-width: 77rem;
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 2rem;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.el-clients .el-satalite-connection .el-year-fiber {
  width: 22%;
  text-align: center;
}

.el-clients .el-satalite-connection .el-year-fiber h2 {
  font-size: 2rem;
  font-weight: 700;
  padding-bottom: 0.5rem;
  color: #fff;
}

.el-clients .el-satalite-connection .el-year-fiber h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
}

/* Section 04 Matters Most */
.metters-most .el-services-main {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  padding-top: 3rem;
}

.metters-most .el-services-main .services-left img {
  width: 100%;
}

.metters-most .el-services-main .services-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 2.5rem;
  padding: 1rem 0 0.8rem;
  max-width: 40rem;
}
.services-content ul li {
  list-style-type: disc;
  padding-bottom: 0.5rem;
}
.services-content .el-top-tier ul {
  padding-left: 1rem;
}
.metters-most .el-services-main .services-content label {
  color: #ff8700;
}

.metters-most .el-services-main .el-lightning-fast {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  padding-top: 2rem;
  justify-content: center;
}

.metters-most .el-services-main .el-lightning-fast .el-robust {
  width: 45%;
  display: flex;
  gap: 1.2rem;
}

.metters-most .el-services-main .el-lightning-fast .el-robust img {
  height: 40px;
  border: 1px dashed #ff8700;
  border-radius: 50%;
  padding: 0.8rem;
}

.metters-most .el-services-main .el-lightning-fast .el-robust h3 {
  font-size: 1.2rem;
  font-weight: 600;
  padding-bottom: 0.7rem;
}

.metters-most .el-services-main .el-lightning-fast .el-robust p {
  font-size: 1rem;
  font-weight: 400;
  max-width: 17rem;
  color: #777777;
}

/* Section Earthlink Fiber Internet */
.el-guarantee .el-tempor .el-plans {
  padding-top: 3rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.el-guarantee .el-tempor {
  position: relative;
  z-index: 2;
}

.el-guarantee .el-rest-easy .el-tempor .el-basic-plan {
  width: 20%;
  text-align: center;
}

.el-guarantee .el-rest-easy .el-tempor .el-basic-plan p {
  color: #777777;
  font-size: 1rem;
  font-weight: 400;
  height: 5.2rem;
}

.el-fiber-near-me {
  background-image: url(/staticfiles/images/el-city-bg.webp);
  background-size: cover;
  opacity: 1;
  padding: 10rem 1.5rem 3.5rem;
  margin-top: -5rem;
  position: relative;
}

.el-fiber-near-me::before {
  background: #ebebebd1;
  content: "";
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  position: absolute;
}

.el-really-fast.el-common-padding.el-guarantee {
  padding: 3.5rem 0;
}

.el-guarantee .el-rest-easy .el-fiber-near-me {
  text-align: center;
}

.el-guarantee .el-rest-easy .el-fiber-near-me h4 {
  font-size: 2rem;
  font-weight: 600;
  color: #121212;
  position: relative;
  z-index: 2;
}

.el-guarantee .el-rest-easy .el-fiber-near-me .el-shop-now {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border: 1px solid #a8a8a8;
  max-width: 30rem;
  margin: 0 auto;
  padding: 0.5rem 0rem 0.5rem 0.8rem;
  margin-top: 1rem;
  position: relative;
  z-index: 2;
}

.el-guarantee .el-rest-easy .el-fiber-near-me .el-shop-now input {
  font-size: 1rem;
  font-weight: 400;
  border: unset;
  outline: none;
}

.el-guarantee .el-rest-easy .el-fiber-near-me .el-shop-now .el-shop-plans a {
  background-color: #0077a4;
  padding: 0.494rem 0.8rem;
  color: #fff;
}

.el-guarantee .el-rest-easy .el-fiber-near-me .el-shop-now .el-shop-plans {
  width: 21.6%;
}

.el-guarantee .el-rest-easy .el-fiber-near-me .el-shop-now .near-me-input {
  width: 35%;
}

.el-guarantee .el-rest-easy .el-fiber-near-me .el-check-address {
  text-align: left;
  margin: 0 auto;
  max-width: 31rem;
  margin-top: 0.8rem;
  position: relative;
  z-index: 2;
}

.el-guarantee .el-rest-easy .el-fiber-near-me label {
  font-size: 1rem;
  font-weight: 500;
}

.el-guarantee .el-rest-easy .el-fiber-near-me .el-check-address input {
  margin: 0;
}

.el-guarantee .el-basic-plan img {
  width: unset;
  height: 60px;
}

.el-guarantee .el-basic-plan::before {
  display: none;
}

.el-guarantee .el-tempor .el-plans .el-basic-plan:hover {
  background-color: #fff;
  opacity: 1;
}

.el-guarantee .el-tempor .el-plans .el-basic-plan:hover h3 {
  color: #ff8700;
}

/* Section Plan for me */

.el-more-tech .el-tempor {
  display: flex;
  align-items: start;
  justify-content: center;
  gap: 2rem;
}

.el-more-tech .el-tempor .el-top-content {
  width: 50%;
  text-align: left;
}

.el-more-tech .el-tempor .el-plans {
  width: 48%;
  justify-content: unset;
  gap: 2rem;
  flex-direction: row;
  padding: 0;
  flex-wrap: nowrap;
}

.el-more-tech .el-tempor .el-plans .el-basic-plan {
  width: 100%;
  box-shadow: none;
  border: 1px solid #777777;
}

.el-more-tech .el-tempor .el-plans .el-basic-plan:hover h4 {
  color: inherit;
}

.el-more-tech .el-tempor .el-top-content h2 {
  max-width: 30rem;
  line-height: 2.8rem;
}

.el-more-tech .el-tempor .el-top-content p {
  max-width: 30rem;
  margin: 0;
  padding-bottom: 3rem;
}

.el-more-tech .el-tempor .el-plans .el-speed-wireless {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.el-more-tech .el-tempor .el-plans .el-speed-wireless.el-change {
  margin-top: -12.5rem;
}

section.el-really-fast.el-common-padding.el-guarantee.el-more-tech {
  padding: 7rem 0 3.5rem;
}

.el-more-tech .el-tempor .el-top-content a {
  background: #0077a4;
  color: #fff;
  padding: 0.8rem;
}

/* section you deserve*/
section.el-really-fast.el-common-padding.el-guarantee.el-more-tech.el-deserve {
  position: relative;
  padding: 3.5rem 0;
  background: linear-gradient(to right, rgb(255, 255, 255), rgb(255 255 255 / 0%)),
    url(/staticfiles/images/el-fade-in-banner.webp);
  background-size: cover;
  background-repeat: no-repeat;
}

@keyframes fadeInBgImage {
  0% {
    background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)),
      url("/staticfiles/images/el-fade-in-banner.webp");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }

  100% {
    background: url("/staticfiles/images/el-fade-in-banner.webp");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 1;
  }
}

section.el-really-fast.el-common-padding.el-guarantee.el-more-tech.el-deserve .el-top-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.el-tempor.el-common-width {
  position: relative;
  z-index: 2;
}

section.el-really-fast.el-common-padding.el-guarantee.el-more-tech.el-deserve .el-top-content p {
  color: #121212;
}

/* section click away */

.el-click-away .el-tempor .el-top-content h2 {
  max-width: 50rem;
  margin: 0 auto;
  line-height: 2.8rem;
}

.el-click-away .el-tempor .el-plans {
  flex-wrap: wrap;
  gap: 2rem;
}

.el-click-away .el-tempor .el-plans .el-basic-plan {
  width: 27.5%;
}

.el-click-away .el-tempor .el-plans .el-basic-plan {
  box-shadow: rgba(6, 24, 44, 0.4) 0px 0px 0px 2px, rgba(6, 24, 44, 0.65) 0px 4px 6px -1px,
    rgba(255, 255, 255, 0.08) 0px 1px 0px inset;
}

.el-click-away .el-tempor .el-just-click-cta {
  background: #ff8700;
  padding: 2.5rem 1.5rem;
  margin-top: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.el-click-away .el-tempor .el-just-click-cta h3 {
  font-size: 1.5rem;
  font-weight: 600;
  padding-bottom: 1.5rem;
}

.el-click-away .el-tempor .el-just-click-cta p {
  font-size: 1rem;
  font-weight: 400;
  padding-bottom: 2rem;
}

.el-click-away .el-tempor .el-just-click-cta a {
  background: #0077a4;
  padding: 0.8rem;
  color: #fff;
}

.el-click-away .el-tempor .el-just-click-cta .cta-content {
  width: 45%;
}

.el-click-away .el-tempor .el-just-click-cta .cta-gift-image {
  width: 25%;
}

/* Section Hard to Reach */

.el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta {
  background: #fff;
  border: 1px solid #a8a8a8;
  margin: 0;
  padding: 0;
  flex-direction: row-reverse;
}

.el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta .cta-content {
  padding: 3rem 0;
  width: 77%;
}

.el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta .cta-gift-image {
  width: 18.4%;
}

.el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta .cta-gift-image img {
  width: 100%;
  margin-bottom: -0.305rem;
}

.el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta .cta-content h3 {
  max-width: 56rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta .cta-content span {
  color: #b2b2b2;
  font-size: 1.5rem;
  font-weight: 500;
}

.el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta .cta-content label {
  color: #ff8700;
}

/* Last Section Home Page */

.el-protect-world .el-tempor.el-common-width {
  max-width: 65rem;
}

.el-click-away.el-hard-to-reach.el-protect-world .el-tempor .el-just-click-cta {
  background: #ff8700;
  margin: 0;
  padding: 0;
  flex-direction: row-reverse;
}

.el-click-away.el-hard-to-reach.el-protect-world .el-tempor .el-just-click-cta .cta-content {
  padding: 0;
  width: 70%;
}

.el-click-away.el-hard-to-reach.el-protect-world .el-tempor .el-just-click-cta .cta-gift-image img {
  margin-bottom: -1rem;
  margin-top: -4rem;
  height: unset;
}

.el-click-away.el-hard-to-reach.el-protect-world .el-tempor .el-just-click-cta span {
  color: #121212;
}

.el-click-away.el-hard-to-reach.el-protect-world .el-tempor .el-just-click-cta h3 {
  padding: 0 0 1rem 0;
  max-width: 41rem;
  font-size: 1.3rem;
  font-weight: 700;
}

.el-click-away.el-hard-to-reach.el-protect-world .el-tempor .el-just-click-cta p {
  max-width: 43rem;
}

.el-click-away.el-hard-to-reach.el-protect-world .el-tempor .el-just-click-cta input {
  background: #fff;
  padding: 0.7rem;
  border: unset;
  outline: unset;
  width: 27rem;
}

.el-clients {
  padding: 0 1.5rem;
}

.el-fiber-near-me .video-learn-more a {
  background-color: #0077a4;
  padding: 0.8rem;
  z-index: 2;
  position: relative;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 10rem;
  margin: 0 auto;
}
.el-top-content h2 {
  text-align: center;
}
.all-cards-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 70.625rem;
  margin: 0 auto;
  padding: 3.6875rem 20px;
}
.all-cards-section.choose-internet-plan {
  align-items: start;
}
.all-cards-section.reversel-resdiental {
  flex-direction: row-reverse;
}
.why-us-section .cards-sec-1 .all-heading {
  text-align: left;
}

.cards-sec-1 {
  width: 43%;
}
.residential-internet-plans {
  width: 52%;
  position: relative;
}
.residential-internet-plans img {
  object-position: center center;
  border-radius: 15px 15px 15px 15px;
  opacity: 0.8;
  position: relative;
  z-index: 999;
}
.residential-internet-plans::before {
  border: 2px solid #ff8700;
  position: absolute;
  top: -10px;
  bottom: -4px;
  left: 80px;
  right: -10px;
  content: "";
  border-radius: 15px;
}
.residential-internet-plans::after {
  position: absolute;
  top: 0;
  bottom: 6px;
  left: 0;
  right: 0;
  content: "";
  background-color: #1b1649;
  border-radius: 15px 15px 15px 15px;
}
.cards-sec-1 img {
  width: 100%;
}
.why-us-section .all-heading p {
  text-align: justify;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.8rem;
  max-width: 29.375rem;
  padding: 0.94rem 0 0.62rem;
}

.our-plans-section .our-process-sec-1 img {
  height: auto;
  width: unset;
}

.list-items ul li {
  list-style: none;
  display: flex;
  align-items: start;
  font-size: 1rem;
  font-weight: 300;
  line-height: 1.5625rem;
  column-gap: 1rem;
  padding-bottom: 0.9rem;
}

.list-items ul li img {
  transform: translate(0px, 5px);
}

.our-plans-section .our-process-sec-1 img {
  height: auto;
  width: unset;
}

.list-items ul li {
  list-style: none;
  display: flex;
  align-items: start;
  font-size: 1rem;
  font-weight: 300;
  line-height: 1.5625rem;
  column-gap: 1rem;
  padding-bottom: 0.9rem;
}

.list-items ul li img {
  transform: translate(0px, 5px);
}

.why-us-section .all-heading h3 {
  color: #0077a4;
}

.our-plans-section .our-process-sec-1 img {
  height: auto;
  width: unset;
}

.list-items {
  padding-top: 0;
}
.all-main .list-items ul {
  padding-top: 0.5rem;
}
.list-items ul li {
  font-size: 1rem;
  font-weight: 300;
  line-height: 1.5625rem;
  column-gap: 1rem;
  padding-bottom: 0.3rem;
  position: relative;
  padding-left: 1.2rem;
}
.list-items ul li::after {
  content: "✔";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  color: #ff8700;
  font-weight: 700;
}
.list-items ul li img {
  transform: translate(0px, 5px);
}
.all-main .secondary-heading {
  font-size: 2rem;
  color: #000;
  font-weight: 600;
  line-height: 2.3125rem;
  margin-top: 0.54rem;
  text-align: center;
}
.why-us-section {
  padding: 3rem 0px;
  background-color: #a8a7a70d;
}
section.el-really-fast.el-common-padding.el-guarantee.el-more-tech.el-deserve h2 {
  text-align: left;
}
@media screen and (max-width: 1400px) {
  .el-fiber-net {
    background-position: right;
    background-size: contain;
  }
}
@media screen and (max-width: 1250px) {
  html {
    font-size: 14.28px;
  }

  .el-click-away .el-tempor .el-just-click-cta {
    justify-content: center;
    gap: 8rem;
  }

  .el-guarantee .el-rest-easy .el-tempor .el-basic-plan {
    width: 35%;
  }

  .el-guarantee .el-rest-easy .el-tempor .el-basic-plan p {
    height: 3rem;
  }

  .wl-consectetur .el-lempor .el-video-content {
    width: 60%;
  }

  .wl-consectetur .el-lempor .el-about-us-video {
    width: 37%;
  }

  .wl-consectetur .el-tempor .el-reliable {
    width: 34%;
  }

  .wl-consectetur .el-tempor .el-top-content {
    width: 60%;
    text-align: left;
  }

  .el-more-tech .el-tempor .el-plans .el-basic-plan p {
    height: 6rem;
  }

  .el-fiber-net .el-your-way p {
    max-width: 30rem;
  }
}
@media screen and (max-width: 985px) {
  html {
    font-size: 15.8px;
  }
  .el-fiber-net::before {
    display: none;
  }
  .el-fiber-net {
    background: unset;
    background-color: #a8a7a70d;
    margin-top: 7.1rem;
  }
  .el-fiber-net .el-banner-yellow-bg .el-your-way {
    width: 100%;
  }
  section.el-fiber-net.el-common-padding {
    position: relative;
  }
  section.el-fiber-net.el-common-padding::after {
    display: none;
  }
  .el-fiber-net .el-your-way h1,
  .el-fiber-net .el-your-way h2,
  .el-fiber-net .el-your-way p,
  .el-fiber-net .el-your-way .el-no-caps li {
    color: #000;
  }
  .el-fiber-net .el-banner-yellow-bg {
    display: block;
  }
  .el-banner-yellow-bg.el-common-width {
    padding: 0;
  }
  .el-fiber-net .el-your-way h1 {
    line-height: 2.8rem;
    padding-bottom: 0.8rem;
    max-width: 37rem;
    font-size: 2.1rem;
    font-weight: 600;
  }
  .el-fiber-net .el-your-way h2 {
    font-weight: 500;
    max-width: 40rem;
    font-size: 1.4rem;
  }
  .el-fiber-net .el-your-way p {
    font-size: 1.2rem;
    text-align: justify;
    margin: 1rem 0;
    line-height: 2rem;
    max-width: 35rem;
  }
  .set-al-icon img {
    width: unset;
    height: 18px;
  }
  .el-fiber-net .el-your-way .el-no-caps li {
    font-size: 1.3rem;
    line-height: 2.9rem;
  }
  .el-common-padding {
    padding: 3.2rem 2.7rem;
  }
}

@media screen and (max-width: 768px) {
  html {
    font-size: 13px;
  }

  .el-fiber-net::before {
    width: 77%;
  }

  .el-really-fast.el-common-padding.el-guarantee {
    padding: 2rem 0;
  }

  .el-tempor .el-top-content h2 {
    line-height: 3rem;
  }

  .el-tempor .el-plans,
  .wl-consectetur .el-tempor,
  .metters-most .el-services-main,
  .metters-most .el-services-main .el-lightning-fast,
  .el-more-tech .el-tempor,
  .el-click-away .el-tempor .el-just-click-cta,
  .el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta {
    flex-direction: column;
    gap: 2rem;
  }

  .el-tempor .el-plans .el-basic-plan {
    width: 80%;
  }

  .wl-consectetur .el-tempor .el-reliable,
  .wl-consectetur .el-tempor .el-top-content,
  .metters-most .el-services-main .services-left,
  .metters-most .el-services-main .services-content,
  .el-click-away .el-tempor .el-just-click-cta .cta-content {
    width: 100%;
  }

  .wl-consectetur .el-tempor .el-top-content span,
  .wl-consectetur .el-tempor .el-top-content h2 {
    text-align: center;
    max-width: unset;
  }

  .metters-most .el-services-main .el-lightning-fast .el-robust {
    width: 80%;
    margin: 0 auto;
  }

  .metters-most .el-services-main .el-lightning-fast .el-robust p,
  .el-more-tech .el-tempor .el-top-content h2 {
    max-width: unset;
  }

  .metters-most .el-services-main .services-content h2 {
    text-align: center;
  }

  section.el-really-fast.el-common-padding.el-guarantee.el-more-tech {
    padding: 2rem 1.5rem;
  }

  .el-more-tech .el-tempor .el-top-content {
    width: 100%;
    text-align: center;
  }

  .el-more-tech .el-tempor .el-top-content p {
    max-width: unset;
    text-align: left;
  }

  .el-more-tech .el-tempor .el-plans .el-speed-wireless.el-change {
    margin: 0;
  }

  .el-more-tech .el-tempor .el-plans {
    width: 100%;
  }

  .el-click-away .el-tempor .el-plans .el-basic-plan {
    width: 70%;
  }

  .el-click-away .el-tempor .el-just-click-cta .cta-gift-image {
    width: 60%;
  }

  .el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta .cta-content {
    padding: 3rem 1.5rem;
    width: 90%;
  }

  .el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta .cta-gift-image {
    width: 100%;
  }

  .el-click-away.el-hard-to-reach.el-protect-world
    .el-tempor
    .el-just-click-cta
    .cta-gift-image
    img {
    margin-bottom: -2.2rem;
    margin-top: -7rem;
    height: unset;
  }

  .el-click-away.el-hard-to-reach.el-protect-world .el-tempor .el-just-click-cta {
    flex-direction: column;
  }

  section.el-really-fast.el-common-padding.el-guarantee.el-more-tech.el-deserve {
    padding: 2rem 0;
  }

  .el-click-away.el-hard-to-reach.el-protect-world .el-tempor .el-just-click-cta .cta-content {
    padding: 2rem 0;
  }

  .el-guarantee .el-rest-easy .el-tempor .el-basic-plan {
    width: 70%;
  }

  .el-guarantee .el-rest-easy .el-tempor .el-basic-plan p {
    height: 6rem;
  }
  .el-click-away.el-hard-to-reach .el-tempor .el-just-click-cta .cta-gift-image img {
    width: 100%;
    transform: scale(0.8);
  }

  .el-click-away.el-hard-to-reach.el-protect-world
    .el-tempor
    .el-just-click-cta
    .cta-gift-image
    img {
    transform: unset;
  }
  .card {
    padding: 0;
  }
  .step {
    width: 100%;
  }
  .step__title::after,
  .step__title::before {
    display: none;
  }
  .step__title {
    height: 33px;
    width: 28%;
    border-radius: 0px 49px 0px 39px;
    font-size: 1.4rem;
  }
  section.el-really-fast.el-common-padding .step p {
    line-height: 2.1rem;
    font-size: 1.4rem;
    text-align: justify;
  }
  .ctaa-buttons {
    margin: 2rem auto 0;
    width: 10.7rem;
  }
  .clients-testimonial .container-reviews {
    flex-direction: column;
    row-gap: 2rem;
    padding: 3rem 2.25rem 1.25rem;
  }
  .clients-testimonial .card-earthlnk {
    margin: 0;
  }
  .clients-testimonial .card-earthlnk p {
    font-size: 1.3rem;
    line-height: 1.6;
    max-width: 33rem;
  }
  .card-earthlnk h4 {
    font-size: 1.5rem;
  }
  .metters-most .el-services-main .el-lightning-fast .el-robust {
    width: 100%;
  }
  .metters-most .el-services-main .el-lightning-fast,
  .el-tempor.el-common-width {
    padding: 0;
  }
  .metters-most .el-services-main .el-lightning-fast .el-robust img {
    height: 27px;
  }
  .metters-most .el-services-main .el-lightning-fast .el-robust h3 {
    font-size: 1.5rem;
  }
  .services-content ul li {
    padding-bottom: 0.2rem;
    font-size: 1.4rem;
    line-height: 2.5rem;
    font-weight: 300;
  }
  .all-cards-section {
    flex-direction: column-reverse;
    row-gap: 3rem;
    padding: 2.8875rem 29px;
  }
  .all-cards-section.reversel-resdiental {
    flex-direction: column-reverse;
  }
  .cards-sec-1 {
    width: 100%;
  }
  .why-us-section .all-heading h3 {
    font-size: 1.7rem;
  }
  .why-us-section .all-heading p {
    font-size: 1.2rem;
    line-height: 2.2rem;
    max-width: 35.475rem;
  }
  .list-items ul li {
    font-size: 1.2rem;
    line-height: 2.0625rem;
  }
  section.el-really-fast.el-common-padding.el-guarantee.el-more-tech.el-deserve {
    background: linear-gradient(to right, hsl(0deg 0% 13% / 70%), #1b1649b8),
      url(/staticfiles/images/el-fade-in-banner.webp);
    height: unset;
    padding: 3rem 2rem;
  }
  section.el-really-fast.el-common-padding.el-guarantee.el-more-tech.el-deserve h2 {
    text-align: center;
    color: #fff;
    font-size: 2rem;
    padding-bottom: 1rem;
  }
  .el-more-tech .el-tempor .el-top-content a {
    padding: 0.8rem 1.6rem;
    display: inline-block;
    font-size: 1.5rem;
  }
  section.el-really-fast.el-common-padding.el-guarantee.el-more-tech.el-deserve .el-top-content p {
    color: #fff;
    font-weight: 400;
    font-size: 1.3rem;
    text-align: center;
    line-height: 2rem;
    padding-bottom: 1.6rem;
  }
  .all-cards-section .ctaa-buttons img {
    width: 16px;
    height: 16px;
    transform: translate(0.4px, -0.1px);
  }
}

@media screen and (max-width: 475px) {
  html {
    font-size: 11px;
  }

  .el-click-away.el-hard-to-reach.el-protect-world .el-tempor .el-just-click-cta input {
    width: 20rem;
  }

  .wl-consectetur .el-lempor {
    flex-direction: column;
    gap: 1.5rem;
  }

  .wl-consectetur .el-lempor .el-about-us-video,
  .wl-consectetur .el-lempor .el-about-us-video img,
  .wl-consectetur .el-lempor .el-video-content {
    width: 100%;
  }

  .el-clients .el-satalite-connection .el-year-fiber {
    width: 40%;
  }

  .el-clients .el-satalite-connection {
    flex-wrap: wrap;
  }

  .el-more-tech .el-tempor .el-plans .el-speed-wireless {
    flex-wrap: nowrap;
    flex-direction: column;
  }

  .el-more-tech .el-tempor .el-plans {
    width: 100%;
    flex-wrap: wrap;
    margin: 0 auto;
  }

  .el-click-away .el-tempor .el-just-click-cta .cta-gift-image,
  .el-click-away .el-tempor .el-just-click-cta .cta-gift-image img {
    width: 100%;
  }

  .el-click-away.el-hard-to-reach.el-protect-world
    .el-tempor
    .el-just-click-cta
    .cta-gift-image
    img {
    margin-bottom: -1.2rem;
    margin-top: -1rem;
  }

  /* .el-guarantee .el-rest-easy .el-fiber-near-me .el-shop-now {
        justify-content: unset;
        gap: 2rem;
    } */
  /* .el-guarantee .el-rest-easy .el-fiber-near-me .el-shop-now .el-shop-plans {
        width: 70%;
    } */
  .el-footor-main .services-el-foot {
    gap: 2.5rem;
  }
}
