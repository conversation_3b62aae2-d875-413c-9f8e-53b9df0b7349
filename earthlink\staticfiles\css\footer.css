@import url("https://fonts.googleapis.com/css2?family=Reddit+Sans:ital,wght@0,200..900;1,200..900&display=swap");

body,
html {
  margin: 0;
  padding: 0;
  font-family: "Reddit Sans";
  box-sizing: border-box;
}

.all-main h1,
.all-main h2,
.all-main h3,
.all-main h4,
.all-main h5,
.all-main h6,
.all-main p,
.all-main ul,
.all-main ol,
.all-main li {
  margin: 0px;
  padding: 0px;
}

.all-main a {
  text-decoration: none;
}

.mw-all-big-sections-space {
  padding: 3rem 1.5rem;
}

.all-main li {
  list-style: none;
}

/* main footer */

.el-footor-main {
  background: #0077a4;
  padding: 3.5rem 1.5rem;
  /* border-bottom: 32px solid #ff8800; */
}

.el-footor-main .el-pages {
  max-width: 60rem;
  margin: 0 auto;
}

.all-footer-icon img {
  width: 15px;
  height: 15px;
}

.el-footor-main .el-log-foot img {
  width: 100%;
  height: 5rem;
}

.el-footor-main .el-log-foot {
  text-align: center;
}

.el-footor-main .el-footer-social {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 1.5rem;
  margin: 2rem 0;
}

.el-footor-main .el-footer-social img,
.el-footor-main .el-loc-mail img {
  height: 30px;
  width: 30px;
  border: 1px dashed #fff;
  padding: 0.5rem;
  border-radius: 50%;
}

.el-footor-main .el-addr-call {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3rem;
}

.el-footor-main .el-loc-mail {
  display: flex;
  align-items: start;
  justify-content: center;
  gap: 0.8rem;
  width: 30%;
  border-right: 1px solid #fff;
  padding-right: 2.5rem;
}

.el-footor-main .el-loc-mail.last-right-none {
  border-right: unset;
  padding-right: unset;
}

.el-footor-main .el-loc-mail h2 {
  font-size: 1.2rem;
  color: #fff;
  font-weight: 500;
}

.el-footor-main .el-loc-mail p,
.el-footor-main .el-loc-mail a {
  font-size: 0.8rem;
  color: #fff;
  font-weight: 400;
}

.el-footor-main .line-gray-footer {
  background: #fff;
  height: 1px;
  margin: 3rem 0;
}

.el-footor-main .services-el-foot {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3rem;
}

.el-footor-main .services-el-foot a {
  font-size: 1rem;
  color: #fff;
  font-weight: 500;
}

.yellow-el-footer {
  display: flex;
  align-items: center;
  background-color: #ff8800;
  height: 40px;
  justify-content: center;
}

.yellow-el-footer h5 {
  font-size: 0.9rem;
  color: #fff;
  font-weight: 300;
}
.el-log-foot {
  width: 50%;
  margin: 0 auto;
  background: #fff;
  padding: 0.2rem 0.9rem;
  border-radius: 10px;
  margin-bottom: 2.5rem;
  display: flex;
}
.ctaa-button.call-now-button {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: none;
  z-index: 9999;
}
.ctaa-button.call-now-button img {
  width: 25px;
  height: 25px;
}
.ctaa-button.call-now-button a {
  background: #ff8600;
  width: 100%;
  text-align: center;
  font-size: 2.3rem;
  font-weight: 700;
  display: flex;
  justify-content: center;
  column-gap: 0.7rem;
  padding: 1.3rem 0;
  align-items: center;
  color: #fff;
}
.copyright-footer .yellow-el-footer {
  background-color: #ff8600;
}

@media screen and (max-width: 900px) {
  html {
    font-size: 13px;
  }
}

@media screen and (max-width: 768px) {
  html {
    font-size: 12px;
  }
  .el-footor-main .el-addr-call {
    flex-wrap: wrap;
  }
  .ctaa-button.call-now-button {
    display: block;
  }
  .copyright-footer {
    margin-bottom: 5.6rem;
  }
  .yellow-el-footer h5 {
    font-size: 1.1rem;
    color: #000;
  }
  .copyright-footer .yellow-el-footer {
    background-color: unset;
  }
}

@media screen and (max-width: 768px) {
  html {
    font-size: 12px;
  }
  .el-footor-main .el-loc-mail {
    width: 100%;
    border-right: unset;
    padding-right: unset;
  }
}
