/* footer */

.all-footer {
  background: #202e39;
  height: 371px;
}

.all-footer-things {
  margin: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.footer-logos-images {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 22px;
}

.footer-list ul,
.site-map-list ul {
  display: flex;
  padding: 0;
  column-gap: 40px;
  flex-wrap: wrap;
  row-gap: 10px;
}
.footer-list ul {
  column-gap: 20px;
}
.footer-list ul li,
.site-map-list ul li {
  list-style: none;
}

.footer-list ul li a,
.site-map-list ul li a {
  text-decoration: none;
  color: #fff;
  font-size: 14px;
  line-height: 25px;
  font-family: "SourceSansPro-Regular";
  font-weight: 400;
  letter-spacing: 1px;
}

.site-map-list ul li {
  position: relative;
}

.site-map-list ul li::after {
  position: absolute;
  top: 0;
  bottom: 0;
  right: -20px;
  content: "";
  height: 16px;
  background-color: #fff;
  width: 1px;
  margin: auto;
}

.footer-buttons,
.sitemap-buttons {
  display: flex;
  justify-content: space-between;
}

.social-icons {
  display: flex;
  column-gap: 20px;
}

.social-icons img {
  width: 30px;
  height: 30px;
}

.footer-lines {
  width: 100%;
  height: 1px;
  background-color: #fff;
}

.copy-right p {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-family: "SourceSansPro-Regular";
  font-weight: 400;
  line-height: 25px;
}
.view-our-policy {
  padding-top: 33px;
}
.view-our-policy a {
  color: #fff;
  font-size: 14px;
  font-family: "SourceSansPro-Regular";
  font-weight: 400;
  line-height: 25px;
  text-decoration: none;
}
.all-footer {
  position: relative;
}
.all-footer::before {
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  right: 0;
  height: 10px;
}
.footer-logos-images img {
  width: unset;
}
@media only screen and (min-width: 1451px) {
  .all-footer-things {
    max-width: 1280px;
  }
}
@media screen and (max-width: 1450px) {
  .all-footer-things {
    max-width: 1100px;
    padding: 0 20px;
  }
}
@media screen and (max-width: 745px) {
  .footer-logos-images,
  .footer-buttons {
    flex-direction: column;
  }
  .footer-list ul li:nth-child(1) {
    display: none;
  }
  .footer-list ul {
    column-gap: 12px;
  }
  .footer-list ul li a,
  .site-map-list ul li a {
    font-size: 12px;
    font-weight: 300;
  }
  .site-map-list ul li:nth-child(2)::after {
    display: none;
  }
  .site-map-list ul {
    column-gap: 26px;
  }
  .site-map-list ul li::after {
    right: -13px;
  }
  .social-icons {
    justify-content: center;
    padding-bottom: 38px;
  }
  .footer-buttons {
    flex-direction: column-reverse;
  }

  .sitemap-buttons {
    flex-direction: row-reverse;
  }
  .footer-logos-images img:nth-child(1) {
    width: 119px;
  }
  .all-footer {
    height: unset;
    padding: 25px 0px;
  }
}
@media screen and (max-width: 389px) {
  .site-map-list ul {
    column-gap: 23px;
  }
  .copy-right p {
    font-size: 12px;
    line-height: normal;
  }
  .sitemap-buttons {
    align-items: center;
  }
  .all-footer-things {
    padding: 0 10px;
  }
}
