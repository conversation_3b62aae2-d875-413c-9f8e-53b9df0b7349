import pytest
import os
import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))  # Assuming flask_app.py is one directory above the tests directory
from viasat.freeze import app
import inspect

@pytest.fixture
def client():
    """Create a test client for the Flask application."""
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

def get_login_required_routes():
    """Get a list of routes that require login."""
    login_required_routes = []
    for rule in app.url_map.iter_rules():
        endpoint = app.view_functions.get(rule.endpoint)
        if endpoint is not None and 'login_required' in inspect.getsource(endpoint):
            login_required_routes.append(rule.rule)
    return login_required_routes

#Final List of routes with all negated rotues
def get_routes():
    """Get a list of all routes registered with the Flask app that don't require login."""
    ignore_list = ['/provider/cox/', '/provider/t-mobile/','/pr/','/blogs/<category_name>','/provider/altice/','/provider/at&t/', '/kinetic-internet/','/provider/spectrum/','/provider/hughes/','/provider/charter/','/zipcode/<zipcode>/','/newpagestesting/','/blog-template/', '/blog-ai/','/staticfiles/<path:filename>', '/display-current/<page_id>', '/ckeditor/staticfiles/<path:filename>' , '/tx/tyler/', '/tx/longview/', '/tx/midland/', '/tx/georgetown/', '/tx/palestine/', '/tx/conroe/', '/tx/arlington/', '/tx/bryan/', '/tx/sherman/', '/tx/irving/', '/tx/denton/', '/tx/spring/', '/tx/weatherford/', '/tx/waco/', '/tx/jarrell/', '/tx/garland/', '/tx/lewisville/', '/tx/mckinney/', '/tx/killeen/', '/tx/laredo/', '/tx/frisco/', '/tx/forney/', '/tx/mansfielf/', '/tx/angleton/', '/tx/texarkana/', '/tx/katy/', '/tx/lubbock/', '/tx/austin/', '/tx/pflugerville/', '/tx/smithville/', '/tx/kingswood/', '/tx/odessa/', '/tx/amarillo/', '/lazy_load_map/', '/<state>/', '/<state>/<city>/']
    # Add Routes that require login
    ignore_list = ignore_list+(get_login_required_routes())
    routes = []
    for rule in app.url_map.iter_rules():
        if rule.rule not in ignore_list:
            routes.append(rule.rule)
    return routes

def test_all_routes(client):
    """Test all routes registered with the Flask application."""
    routes = get_routes()
    for route in routes:
        print(route)
        response = client.get(route)
        assert response.status_code == 200 or response.status_code == 301, f"Failed to get status 200 for route: {route}"