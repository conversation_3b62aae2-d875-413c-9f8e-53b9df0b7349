
<!-- fddfdf -->
<header class="header-area" id="myHeader">
    <!-- site-navbar start -->
    <div class="navbar-area">
        <div class="container">
            <nav class="site-navbar">
                <!-- site menu/nav -->
                <div class="menu" id="menu">
                    <div class="logo">
                        <a href="/"><img src="/staticfiles/hughsnet-logo.png" alt=""></a>
                    </div>
                    <ul class="header-button">
                        <li><a href="/">Home</a></li>
                        <li><a href="/internet">Internet</a></li>
                        <li><a href="/phone">Phone</a></li>
                        <li><a href="/plans">Plan</a></li>
                        <li><a href="/customer-service">Customer Service</a></li>
                        <li><a href="/business">Business</a></li>
                    </ul>
                </div>
                <div class="menu hire-developer-btn">
                    <a href=tel:8888730351 class="hire-dev"><img
                            src="/staticfiles/hughsnet-home-page-header-phone-icon.webp" alt="">Call to Order</a>
                </div>


                <!-- nav-toggler for mobile version only -->
                <button class="nav-toggler">
                    <span></span>
                </button>
            </nav>
        </div>
    </div>

    <script>
        ///header
        // define all UI variable
        const navToggler = document.querySelector(".nav-toggler");
        const navMenu = document.querySelector(".site-navbar ul");
        const navLinks = document.querySelectorAll(".site-navbar a");

        // load all event listners
        allEventListners();

        // functions of all event listners
        function allEventListners() {
            // toggler icon click event
            navToggler.addEventListener("click", togglerClick);
            // nav links click event
            navLinks.forEach((elem) => elem.addEventListener("click", navLinkClick));
        }

        // togglerClick function
        function togglerClick() {
            navToggler.classList.toggle("toggler-open");
            navMenu.classList.toggle("open");
        }

        // navLinkClick function
        function navLinkClick() {
            if (navMenu.classList.contains("open")) {
                navToggler.click();
            }
        }
    </script>
    <script>


        // 	sticky
        // JavaScript to handle the sticky behavior
        window.onscroll = function () {
            makeHeaderSticky();
        };

        var header = document.getElementById("myHeader");
        var sticky = header.offsetTop + 50;

        function makeHeaderSticky() {
            if (window.pageYOffset > sticky) {
                header.classList.add("sticky");
            } else {
                header.classList.remove("sticky");
            }
        }		
    </script>
</header>
</body>