:focus-visible{
    border: none;
    outline: none;
}
.blog-main img{
    width: unset;
    max-width: 100%;
}
.blog-container{
    max-width: 1780px;
    padding: 0 1.25rem;
    margin: 0 auto;
}
.blog-banner-section .blog-container{
    width: 100%;
} 
/*banner section css is start from here*/
.blog-banner-section{
    min-height: 30rem;
    display: flex;
    align-items: center;
    justify-content: start;
    background-image: url(/staticfiles/hughesnet-blog-banner.webp);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}
.blog-banner-content {
    max-width: 55.45rem;
}
.blog-banner-content img {
    margin-bottom: 1rem;

}
.blog-banner-content h1 {
    font-size: 2rem;
    line-height: 1.3;
    color: #fff;
    font-weight: 700;
    margin-bottom: .775rem;
}
.blog-banner-content h1 span{
    color: #FF5820;
}
.blog-banner-content p {
    font-size: .975rem;
    line-height: 1.5;
    color: #fff;
}
/* blog posts section css is start from here*/
.blog-posts-section{
    padding: 2.5rem 0;
    background-color: #fff;
}
.blog-post-search {
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 1.875rem;
    margin-bottom: 2.5rem;
}
.blog-form-grp {
    position: relative;
    width: 22.125rem;
    display: flex;
    align-items: stretch;
    flex-direction: column;
}
.blog-form-grp input{
    border: 1px solid #0091C4;
    border-radius: 100px;
    padding: .8125rem 1.25rem;
    font-size: 1.125rem;
    color: #000;
    font-weight: 400;
    line-height: 1;
}
.blog-form-grp input::placeholder{
    font-size: 1.125rem;
    color: #000;
    font-weight: 400;
    line-height: 1;    
}
.blog-form-grp button {
    border: none;
    background-color: transparent;
    position: absolute;
    top: .8375rem;
    right: .525rem;
    cursor: pointer;
}
.blog-form-grp button img {
    width: 1.475rem;
    height: 1.475rem;
}
.categories-button button {
    border: 1px solid #0091C4;
    padding: .8125rem 1.25rem;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
    font-size: 1.125rem;
    line-height: 1;
    font-weight: 400;
}
.categories-button button img{
    margin-left: .9375rem;
}
.dropdown-content ul {
    list-style: none;
    padding: 0;
}
.dropdown-content ul li{
    margin: .3125rem 0;
    padding: .625rem;
    cursor: pointer;
    text-align: center;
}
.dropdown-content ul li:hover{
    background-color: #0091c4f2;
    color: #fff;
}
.dropdown-content {
    display: none;
    position: absolute;
    background-color: #f1f1f1;
    min-width: 10rem;
    overflow: auto;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
  }
  .dropdown-content a {
    color: black;
    padding: .75rem 1rem;
    text-decoration: none;
    display: block;
  }  
  .dropdown a:hover {background-color: #ddd;}
  .show {display: block;}
.date-comunication{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}
.date-comunication p{
    color: #FF5820;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1;
}
.read-more{
    background-color: #0091C4;
    border: 2px solid #0091C4;
    padding: .75rem 1.875rem;
    font-size: 1.125rem;
    color: #fff;
    font-weight: 700;
    display: inline-block;
    border-radius: .25rem;
    transition: 0.3s;
    
}
.read-more:hover{
    background-color: #fff;
    color: #0091C4;
}
.posts-wraper {
    display: flex;
    align-items: stretch;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1.25rem;
}
.posts-box {
    width: 24%;
    border: 1px solid #0091C4;
    border-radius: .75rem;
    overflow: hidden;
}
.posts-box > img {
    width: 100%;
    object-fit: cover;
}
.posts-content {
    padding: 1.25rem .9375rem;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    align-items: start;
    justify-content: start;
}
.posts-content h4{
    font-size: 1.125rem;
    font-weight: 700;
    color: #0091C4;
    
}
.posts-content p {
    font-size: .875rem;
    line-height: 1.5;
}
.pagination {
    display: flex;
    list-style: none;
    padding: 0;
}
.pagination li {
    margin: 0 .3125rem;
}
.pagination a {
    text-decoration: none;
    color: #0078D7;
    font-size: 1rem;
    width: 1.875rem;
    height: 1.875rem;
    line-height: 2;
    text-align: center;
    display: inline-block;
    border-radius: 50%;
    border: 1px solid #0078D7;
    display: flex;
    align-items: center;
    justify-content: center;

}
.pagination .active a ,
.pagination a:hover{
    background-color: #0078D7;
    color: #ffffff;
}
.blog-posts-pagination {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}
.cta-section{
    min-height: 30rem;
    display: flex;
    align-items: center;
    justify-content: start;
    background-image: url(/staticfiles/hughesnet-blog-cta-banner.webp);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}
.cta-section .blog-container{
    width: 100%;
}
.cta-content {
    max-width: 35rem;
}
.cta-content h2 {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.3;
    color: #000;
    margin-bottom: 2.5rem;
}
.cta-content h2 span{
    display: block;
    color: #0091C4;
}
.cta-content h4{
    font-size: 2rem;
    font-weight: 500;
    line-height: 1.3;
    color: #0091C4;
}

/*inner blog post css is start from here*/
.inner-blog-post-section{
    min-height: 30rem;
    display: flex;
    align-items: center;
    justify-content: start;
   background-color: #0091C4; 
} 
.blg-pst-cnt{
    max-width: 48rem;
}
.blg-pst-cnt h1{
    font-size: 2rem;
    line-height: 1.3;
    font-weight: 700;
    color: #fff;
    margin-bottom: 2.5rem;
}
.blg-pst-cnt p{
    font-size: .875rem;
    line-height: 1.3;
    font-weight: 400;
    color: #fff;

}
.inner-blog-post-section .blog-container{
    width: 100%;
}
.share_on{
    display: flex;
    align-items: center;
    justify-content: start;
    gap: .9375rem;
    margin-top: 2.5rem;
}
.share_on ul {
    list-style: none;
    padding: 0;
    display: flex;
    align-items: center;
    gap: .9375rem;
    margin: 0;
}
.share_on h4{
    font-size: 1.125rem;
    line-height: 1.3;
    font-weight: 700;
    color: #fff;
   
}
.left-side {
    width: 65%;
}
.right-side {
    width: 33%;
}
.main-innner-post-content p,
.main-innner-post-content ol li,
.main-innner-post-content ul li{
    font-size: .875rem;
    line-height: 1.4;
    color: #000;    
}
.main-innner-post-content ol,
.main-innner-post-content ul{
    padding: 0;
    list-style-position: inside;
}
.main-innner-post-content p{
    margin: .625rem 0;
}
.main-innner-post-content h2{
    font-size: 1.375rem;
    line-height: 1.3;
    font-weight: 600;
}
.main-innner-post-content h3{
    font-size: 1.375rem;
    line-height: 1.3;
    font-weight: 600;
}
.main-innner-post-content h2,
.main-innner-post-content h3{
    margin: 1.5625rem 0 .9375rem  0;
}
.left-side .main-inner-post > img{
    width: 100%;
}
.recent-articles-content h4{
    font-size: 1.375rem;
    line-height: 1.3;
    font-weight: 600;
}
.recent-articles-content p {
    font-size: .875rem;
    line-height: 1.3;
    text-align: left;
}
.recent-articles-content .articles-date{
    font-size: .875rem;
    line-height: 1;
    color: #0091C4;
}
.recent-articles-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.sides-wraper {
    display: flex;
    align-items: start;
    justify-content: space-between;
}
.recent-articles > h3{
    font-size: 1.375rem;
    line-height: 1.3;
    font-weight: 700;
    color: #0091C4;
}
/* responsive css is statr from here */
.recent-articles-box {
    display: flex;
    align-items: stretch;
    justify-content: start;
    gap: .9375rem;
    margin-top: 2.5rem;
}
.inner-blog-main-section{
    padding: 2.5rem 0;
}
.blog-posts-section-content h3{
    text-align: left;
    font-size: 1.375rem;
    line-height: 1.3;
    font-weight: 700;
    color: #0091C4;
    margin-bottom: 40px;
}
.right-side {
    position: sticky;
    top: 6.5625rem;
}
.banner-flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@media screen and (max-width:1500px) {
    .posts-box {
        width: 23%;
    }
}
@media screen and (max-width:1024px) {
    .posts-box {
        width: 30.8%;
    }
    .banner-flex > img {
        width: 39%;
    }
    .inner-blog-post-section {
        min-height: 23rem;
    }
    .sides-wraper {
        flex-direction: column;
    }
    .left-side {
        width: 100%;
    }
    .right-side {
        width: 100%;
    }
}
@media screen and (max-width:768px) {
    .posts-box {
        width: 48%;
    }   
    .banner-flex > img{
        display: none;
    }
}
@media screen and (max-width:568px) {

    .posts-box {
        width: 100%;
    }
    .blog-post-search {
        gap: 1.175rem;
        flex-direction: column;
        align-items: end;
    }
    .blog-banner-content h1 {
        font-size: 1.4rem;
    }
    .cta-content h2 {
        font-size: 1.5rem;
    }
    .cta-content h4 {
        font-size: 1.3rem;
    }
    .cta-content .main-button {
        justify-content: start;
    }
    .cta-section {
        min-height: 20.2rem;
    }
    .blog-banner-section {
        min-height: 25rem;
    }
}
@media screen and (max-width:426px){
    .recent-articles-box {
        margin-top: 40px;
        flex-direction: column;
    }
    .recent-articles-content p{
        margin: 15px 0;
    }
}