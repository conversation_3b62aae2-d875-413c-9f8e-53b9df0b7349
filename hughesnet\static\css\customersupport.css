.customersupport-banner .banner-background {
  background: url(/staticfiles/hughesnet-customer-support-banner-image.webp);
}

.customersupport-banner .hughsenet-heading p {
  max-width: 683px;
}

.customersupport-banner .hughsenet-heading label {
  font-size: 40px;
}

.customersupport-banner .hughsenet-heading h2 {
  color: #ff5820;
  font-size: 30px;
  font-weight: 700;
  line-height: 22px;
  margin: 15px 0px 30px;
  line-height: 1.3;
}

.hughsnet-phone-number p {
  font-size: 22px;
  font-weight: 600;
  line-height: 22px;
  margin-bottom: 5px;
}

.hughsnet-phone-number a {
  color: #0091c4;
  font-size: 30px;
  font-weight: 700;
  line-height: 40px;
}

.phone-espanol {
  margin-top: 30px;
}

/* way to contact */
.Ways-to-Contact {
  padding: 64px 0px;
}
.Ways-to-Contact-boxes {
  display: flex;
  justify-content: space-between;
  max-width: 1620px;
  margin: 0 auto;
  padding: 0 20px;
}

.Ways-to-Contact-box-1 {
  width: 35%;
}

.Ways-to-Contact-box-2 {
  width: 60%;
}

.Ways-to-Contact-box-1 h2 {
  color: #000;
  font-size: 36px;
  font-weight: 600;
  line-height: 46px;
  margin-bottom: 31px;
}

.Ways-to-Contact-box-1 h2 span {
  color: #0091c4;
}

.phone-call-heading-hughsnet {
  display: flex;
  column-gap: 17px;
  align-items: center;
  padding-bottom: 22px;
}

.hughsnet-phone-call {
  display: flex;
  column-gap: 55px;
  margin-bottom: 96px;
  align-items: center;
}

.phone-call-heading-hughsnet {
  color: #000;
  font-size: 24px;
  font-weight: 600;
  line-height: 30px;
}

.phone-call-heading-hughsnet span {
  color: #0091c4;
}

.husghsnet-phone-call1 p {
  color: #000;
  text-align: justify;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}
.Ways-to-Contact-box-1 p {
  margin-top: 55px;
}
.need-help-techinacal .glace-1,
.need-help-techinacal1 .glace-1 {
  align-items: center;
}
.need-help-techinacal {
  background: transparent;
}
.customer-page-data-saving-feacture .at-all-glace {
  background-image: url(/staticfiles/hughsnet-phone-page-lot-more-section.webp);
  background-repeat: no-repeat;
}
.all-main .connecet-throught {
  text-align: left;
}
