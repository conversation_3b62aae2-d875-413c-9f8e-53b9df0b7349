.all-main img {
  width: 100%;
}
.kinetic {
  background-image: url(/staticfiles/blog-Banner-image.webp);
  background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  height: 30.75rem;
  display: flex;
  align-items: center;
}
.banner-img {
  padding: 0px 7.8rem;
}
.all-main h1 {
  font-size: 2.5rem;
  font-weight: 700;
}
.all-main h1 span {
  background: linear-gradient(272deg, #22a881 0%, #007c83 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.featured-post-page {
  background-color: #fff;
}
.Blog-page-content {
  max-width: 76.25rem;
  margin: 0 auto;
  padding: 0 1.75rem;
}
.all-main h2 {
  font-size: 1.875rem;
  line-height: 2.5rem;
  font-weight: 700;
  padding: 2rem 0;
}
.featured-post {
  display: flex;
  justify-content: space-between;
  max-width: 76.25rem;
  margin: 0 auto;
  padding-bottom: 2rem;
}
.featured-post-img {
  width: 63%;
}
.featured-post-img img {
  border-radius: 0.4375rem;
}
.featured-post-content {
  width: 33%;
  padding: 1rem 0 0;
}
.featured-post-content .para {
  line-height: 1rem;
  font-size: 0.75rem;
  padding-bottom: 0.5rem;
}
.all-main h3 {
  font-size: 1.25rem;
  line-height: 1.6875rem;
  font-weight: 700;
}
.heading-h3 {
  padding-bottom: 1.5rem;
}
.all-main p {
  line-height: 1.1875rem;
  font-size: 1rem;
  font-weight: 400;
}
.detail-box {
  display: flex;
  padding-top: 1rem;
  gap: 0.5rem;
  align-items: center;
}
.box-img {
  display: flex;
}
.box-img img {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 3.125rem;
  background-size: contain;
}
.line {
  width: 100%;
  height: 0.0313rem;
  background-color: #000;
  opacity: 0.6;
}
.featured-post-links {
  display: flex;
  justify-content: space-between;
  padding: 0 1.75rem;
}
.links {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  list-style: none;
  margin: 1.5rem 0;
  padding: 0;
}
.links li a {
  color: #000;
  font-size: 1rem;
  padding: 0.5rem;
  border: 2px solid #fff;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  border-radius: 0.25rem;
  background: #fff;
}
.search-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search-btn input {
  background-color: #f4f4f4;
  border-radius: 2rem;
  border: 2px solid #fff;
  padding: 0.4375rem 0 0.4375rem 0.75rem;
  line-height: 1.6875rem;
  font-size: 1rem;
  width: 100%;
}
button {
  background: transparent;
  outline: none;
  border: none;
  margin-left: -40px;
}
.search-btn img {
  width: 1.25rem;
  height: 1.25rem;
}
.recent-post {
  max-width: 76.25rem;
  margin: 0 auto;
  padding: 0 1.75rem;
}
.post-data {
  display: flex;
  flex-wrap: wrap;
  gap: 1.75rem;
  justify-content: space-between;
}
.post-details {
  width: 31%;
  border-radius: 2px;
  border: 1px solid #fff;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
.post-img img {
  border-radius: 6px;
}
.post-content {
  background: linear-gradient(272deg, #22a881 0%, #007c83 100%);
  margin-top: -50px;
  margin-left: 45px;
  margin-right: -15px;
  color: #fff;
  padding: 0.2rem 0.9rem;
  border-radius: 6px;
}
.post-content h3,
p {
  padding: 0.5rem;
}
.detail-boxes {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.post-content .detail-box {
  padding: 0.6rem 0.3rem;
}

.pagination {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.pagination ul {
  list-style: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
}
.pagination ul li img {
  display: flex;
}
.footer-content-sec {
  max-width: 76.25rem;
  margin: 0 auto;
  padding: 0 1.75rem;
}
.footer-content-boost {
  background: linear-gradient(272deg, #22a881 0%, #007c83 100%);
  border-radius: 60px 0px 0px 0px;
  padding: 3rem 0;
}
.boost-your-internet-content {
  display: flex;
  justify-content: space-between;
}
.footer-img img {
  border-radius: 0px 70px 0px 70px;
  margin-top: -7.1875rem;
}
.boost-footer {
  width: 57%;
}
.footer-img {
  width: 33%;
}
.boost-footer h3 {
  font-size: 2.125rem;
  line-height: 3rem;
  color: #fff;
  font-weight: 700;
  padding-bottom: 1rem;
}
.boost-footer .para-data {
  color: #fff;
  font-size: 1.5rem;
  line-height: 2.5rem;
  padding-bottom: 1.5rem;
}
.call-btn {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.125rem;
  padding: 0.825rem 1.3125rem;
  border-radius: 0.25rem;
  color: #fff;
  background: #ef6724;
  display: inline;
}
.call-btn img {
  width: unset;
  transform: translate(-0.25rem, 0.25rem);
}
.call-btn a {
  color: #fff;
}
@media screen and (max-width: 1023px) {
  .featured-post {
    flex-direction: column;
  }
  .featured-post-img {
    width: 100%;
  }
  .featured-post-content {
    width: 100%;
    text-align: center;
  }
  .featured-post-content .detail-box {
    gap: unset;
    justify-content: center;
  }
  .post-details {
    width: 45%;
  }
  .footer-content-boost {
    border-radius: unset;
  }
  .boost-your-internet-content {
    flex-direction: column;
    text-align: center;
    row-gap: 3.125rem;
  }
  .boost-footer {
    width: 100%;
  }
  .footer-img {
    width: 100%;
    text-align: center;
  }
  .footer-img img {
    margin-top: 0;
    border-radius: unset;
  }
  .search-btn img {
    width: 1.25rem;
  }
}
@media screen and (max-width: 767px) {
  .kinetic{
    height: 12.75rem;
  }
  .featured-post-links {
    flex-direction: column;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    justify-content: center;
    padding: 0;
  }
  .featured-post-img img{
    border-radius: unset;
  }
  .search-btn button {
    margin-left: -2.5875rem;
  }
  .search-btn img {
    width: 0.9375rem;
  }
  .links {
    gap: 1rem;
    row-gap: 2.3rem;
    justify-content: center;
  }
  .post-data {
    flex-direction: column;
    align-items: center;
  }
  .post-details {
    width: 100%;
  }
  .post-details img{
    border-radius: unset;
  }
  .post-content {
    margin-left: unset;
    margin-top: -0.2812rem;
    margin-right: unset;
    border-radius: unset;
  }
}
@media screen and (max-width:400px) {
  .search-btn {
    width: 80%;
  }
  .links{
    flex-wrap: wrap;
  }
}