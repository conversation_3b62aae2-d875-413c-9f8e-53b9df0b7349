.all-main img {
  width: 100%;
}
.banner {
  background-image: url(/staticfiles/blog-post-banner-image.webp);
  background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  height: unset;
  padding-top: 3.938rem;
  padding-bottom: 5.875rem;
}
.banner-content {
  max-width: 67rem;
  padding: 0px 1rem;
  margin: 0px auto;
}
.banner-content h1 {
  max-width: 36rem;
  padding-bottom: 0.625rem;
  font-size: 2.1rem;
  font-weight: 700;
  line-height: 2.5rem;
}
.banner-content p {
  padding-bottom: 0px;
  color: rgb(102, 102, 102);
  font-size: 1rem;
  max-width: 35rem;
  line-height: 1.9rem;
}
.we-focus {
  background: rgb(249, 249, 249);
}
.what-we-focus {
  display: flex;
  gap: 2rem;
  justify-content: space-between;
  max-width: 66.3rem;
  position: relative;
  margin: 0px auto;
  padding: 3rem 2rem;
}
.we-focus-content {
  width: 67%;
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0.75) 0px 0px 30px -20px;
  padding: 0px;
  margin-top: -6.4rem;
  background: rgb(255, 255, 255);
}
.we-focus-content-data {
  padding: 0.9603rem 1.563rem;
}
.we-focus-content-data p {
  font-size: 1rem;
  color: rgb(29, 29, 29);
  text-align: justify;
  padding: 0 0 1rem 0;
  font-weight: 400;
}
.we-focus-content-data h2 {
  font-size: 1.875rem;
  line-height: 1.3;
  color: #000;
  margin: 1.5rem 0 0.7rem 0;
  padding: 0px;
  font-weight: 700;
}
.we-focus-content-data h3 {
  font-size: 1.375rem;
  font-weight: 600;
  line-height: 1.3;
  color: #000;
  margin: 1.5rem 0 0.7rem 0;
  padding: 0px;
}
.we-focus-content-data h4{
  font-size: 1.2rem;
  line-height: 1.3125rem;
  font-weight: 600;
}
.we-focus-content-data ul {
  padding: 0 0 0 2.25rem;
  margin-bottom: 1.8rem;
}
.we-focus-content-data ul li {
  text-decoration: none;
  color: #1d1d1d;
  font-size: 1rem;
  line-height: 1.4;
  margin-bottom: 0.4rem;
}
.about-author {
  width: 30%;
  text-align: center;
}
.author-detail {
  margin-bottom: 1.5rem;
  display: flex;
  gap: 2rem;
  align-items: center;
}
.about-author-card {
  padding: 1.25rem 1rem;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.author-img {
  display: flex;
  border-radius: 3.125rem;
  background-size: contain;
}
.author-content p {
  color: #ff8f00;
}
.author-content-detail p {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6rem;
  max-width: 42rem;
}
.social-images {
  display: flex;
  gap: 1rem;
}
.social-images img {
  width: 1.3rem;
  height: 1.3rem;
}
.internet-details {
  border: none;
  padding: 1.875rem;
  background: linear-gradient(272deg, #22a881 0%, #007c83 100%);
  margin-top: 2rem;
}
.we-focus-content-img img {
  width: 100%;
}
.internet-details h3 {
  font-size: 2.375rem;
  line-height: 3.1875rem;
  color: #fff;
  font-weight: 500;
  padding-bottom: 1rem;
}
.internet-details p {
  font-size: 1.875rem;
  color: #fff;
  font-weight: 400;
  padding-bottom: 1rem;
  line-height: 2.5rem;
  max-width: 42rem;
}
.boost-call-btn {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.125rem;
  padding: 0.925rem 1.3125rem;
  border-radius: 0.25rem;
  color: #fff;
  background: #ef6724;
  display: inline;
}
.boost-call-btn a {
  color: #fff;
  text-decoration: none;
}
.boost-call-btn img {
  width: unset;
  transform: translate(-0.25rem, 0.25rem);
}
.related-posts {
  max-width: 78rem;
  margin: 0 auto;
  padding: 2rem 2rem;
}
.related-posts h2{
  font-size: 1.875rem;
  line-height: 2.5rem;
  font-weight: 600;
}
.related-posts-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.related-posts-box {
  width: 31%;
  border-radius: 2px;
  border: 1px solid #fff;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding-bottom: 2rem;
}
.posts-box-img {
  display: contents;
}
.posts-box-img img {
  border-radius: 6px;
  margin-bottom: 1rem;
}
.posts-box-data p {
  font-size: 1.25rem;
  line-height: 1.6875rem;
  font-weight: 400;
  margin: 0.5rem;
}
.post-box-author {
  display: flex;
  align-items: center;
}
.box-author {
  display: flex;
  padding: 0.6rem 0.3rem;
  align-items: center;
}
.box-author-image {
  display: flex;
}
.box-author-image img {
  width: 2rem;
  height: 2rem;
  border-radius: 3.125rem;
  background-size: contain;
}
.box-author-data p {
  line-height: 1.1875rem;
  font-size: 0.875rem;
  color: rgb(102, 102, 102);
}
.internet-connectivity {
  background: linear-gradient(272deg, #22a881 0%, #007c83 100%);
  border-radius: 60px 0px 0px 0px;
  padding: 2rem 0;
}
.internet-connectivity-sec {
  max-width: 78rem;
  margin: 0 auto;
  padding: 0 2rem 2rem;
}
.internet-connectivity-details {
  display: flex;
  justify-content: space-between;
}
.internet-connectivity-data {
  width: 57%;
}
.internet-connectivity-img{
  width: 33%;
}
.internet-connectivity-data h3 {
  font-size: 2.125rem;
  line-height: 3rem;
  color: #fff;
  font-weight: 700;
  margin: 0;
}
.internet-connectivity-data p {
  color: #fff;
  font-size: 1.5rem;
  line-height: 2.5rem;
  padding-bottom: 1.5rem;
}
.internet-connectivity-call-btn {
  font-size: 0.975rem;
  font-weight: 500;
  line-height: 1.125rem;
  padding: 0.925rem 1.3125rem;
  border-radius: 0.25rem;
  color: #fff;
  background: #ef6724;
  display: inline;
}
.internet-connectivity-call-btn a {
  color: #fff;
  text-decoration: none;
}
.internet-connectivity-call-btn img {
  width: unset;
  transform: translate(-0.25rem, 0.25rem);
}
.internet-connectivity-img img {
  border-radius: 0px 70px 0px 70px;
  margin-top: -6.1875rem;
}
@media screen and (max-width: 1023px) {
  .what-we-focus {
    display: block;
  }
  .we-focus-content {
    width: 100%;
  }
  .about-author {
    margin-top: 2rem;
    width: 100%;
  }
  .author-detail {
    justify-content: center;
  }
  .author-content-detail {
    text-align: center;
  }
  .author-content-detail p {
    max-width: none;
  }
  .social-images {
    justify-content: center;
  }
  .internet-details {
    text-align: center;
  }
  .internet-details p {
    max-width: none;
  }
  .related-posts-box {
    width: 100%;
  }
  .related-posts-content {
    flex-direction: column;
    align-items: center;
  }
  .posts-box-data {
    text-align: center;
  }
  .post-box-author {
    justify-content: center;
  }
  .internet-connectivity {
    border-radius: unset;
  }
  .internet-connectivity-details {
    flex-direction: column;
    text-align: center;
    row-gap: 3.125rem;
  }
  .internet-connectivity-img {
    width: 100%;
    text-align: center;
  }
  .internet-connectivity-data {
    text-align: center;
    width: 100%;
  }

  .internet-connectivity-img img {
    margin-top: 0;
    border-radius: unset;
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .posts-box-img img{
    border-radius: unset;
  }
  .boost-call-btn img {
    width: 0.9375rem;
  }
  .internet-connectivity-call-btn img {
    width: 0.9375rem;
  }
}