server {

listen 80;

server_name mwmab.com www.mwmab.com;


location / {

return 301 https://www.mwmab.com$request_uri;

}

}


server {

listen 443 ssl http2;

server_name www.mwmab.com;


ssl_certificate /etc/letsencrypt/live/mwmab.com/fullchain.pem; # managed by Certbot
ssl_certificate_key /etc/letsencrypt/live/mwmab.com/privkey.pem; # managed by Certbot


    # Define the allowed SSL protocols and ciphers

ssl_protocols TLSv1.2 TLSv1.3;

ssl_prefer_server_ciphers off;

ssl_ciphers 'TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384';


    # Additional SSL settings (e.g., HSTS) can go here


location / {

proxy_pass http://127.0.0.1:5001;

include /etc/nginx/proxy_params;

}

}


server {

listen 443 ssl http2;

server_name mwmab.com;


ssl_certificate /etc/letsencrypt/live/mwmab.com/fullchain.pem; # managed by Certbot
ssl_certificate_key /etc/letsencrypt/live/mwmab.com/privkey.pem; # managed by Certbot


    # Redirect non-www to www

return 301 https://www.mwmab.com$request_uri;

}