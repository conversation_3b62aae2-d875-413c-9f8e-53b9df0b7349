<?xml version="1.0" encoding="UTF-8"?>

<urlset
      xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
            http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">

{% for url in static_urls %}
  {% set modified_url = url["loc"].replace("www.", "")%}
  {% if modified_url not in excluded_urls %}
    {% if modified_url not in ['https://bestsatelliteinternetservice.com/sitemap.xml', 'https://bestsatelliteinternetservice.com/robots.txt/', 'https://bestsatelliteinternetservice.com/post-sitemap.xml', 'https://bestsatelliteinternetservice.com/page-sitemap.xml', 'https://bestsatelliteinternetservice.com/sitemap_index.xml'] %}
      <url>
        <loc>{{ modified_url }}</loc>
        {% if modified_url == 'https://bestsatelliteinternetservice.com/' %}
          <lastmod>{{ file_info.get('index', '') }}</lastmod>
          <priority>1.00</priority>
        {% else %}
          {% set url_key = modified_url.split('/')[-1] %}
          <lastmod>{{ file_info.get(url_key, '') }}</lastmod>
          <priority>0.80</priority>
        {% endif %}
      </url>
    {% endif %}
  {% endif %}
{% endfor %}


</urlset>