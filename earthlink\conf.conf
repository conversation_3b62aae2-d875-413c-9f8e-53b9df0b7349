

# HTTP server block

server {

listen 80;

server_name ultimateinternetplans.com www.ultimateinternetplans.com;


    # Redirect all HTTP traffic to HTTPS with appropriate host

if ($host = 'www.ultimateinternetplans.com') {

return 301 https://ultimateinternetplans.com$request_uri; # Redirect www to non-www

}


if ($host = 'ultimateinternetplans.com') {

return 301 https://$host$request_uri; # Continue redirecting to HTTPS

}


    # Fallback redirect, should not be needed but included for completeness

return 301 https://ultimateinternetplans.com$request_uri;

}


# HTTPS server block for www.ultimateinternetplans.com

server {

listen 443 ssl http2;

server_name www.ultimateinternetplans.com;


    # SSL configuration

ssl_protocols TLSv1.2 TLSv1.3;

ssl_prefer_server_ciphers off;

ssl_ciphers 'TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384';


    # Redirect www to non-www

return 301 https://ultimateinternetplans.com$request_uri;
}


# HTTPS server block for ultimateinternetplans.com

server {

listen 443 ssl http2;

server_name ultimateinternetplans.com;



location / {

        # Proxy settings assuming your Flask app is on the same server listening on port 5005

proxy_pass http://127.0.0.1:5015;

include /etc/nginx/proxy_params;

}

}
