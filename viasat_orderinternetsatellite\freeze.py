from flask import Flask, redirect, render_template, make_response, request, send_file
from flask_cors import CORS
from urllib.parse import urlparse, urljoin
import datetime, pytz, os

# from flask_frozen import Freezer

app = Flask(__name__, static_folder='staticfiles')
# app.config['FREEZER_DESTINATION'] = 'build'
# freezer = Freezer(app)

allowed_origins = [
    "http://127.0.0.1:8000",  # Replace with your same origin URL
    "https://d3rho3py3wd5ig.cloudfront.net",
    "https://widget-v4.tidiochat.com",
    "https://*.googletagmanager.com",
    "https://*.google-analytics.com",
    "https://*.analytics.google.com",
    "https://*.g.doubleclick.net",
    "https://*.google.com",
    "https://*.google.com.pk",
    "https://www.googleadservices.com",
    "https://googleads.g.doubleclick.net",
    "https://bid.g.doubleclick.net",
    "https://cdnjs.cloudflare.com/ajax/libs/*",
    "http://127.0.0.1:5010"

]

CORS(app, origins = allowed_origins, methods=["GET"])
app.config['CORS_HEADERS'] = 'Content-Type'

# CDN = "https:/d3rho3py3wd5ig.cloudfront.net/"
CDN = "/"

secret_key = '5accdb11b2c10a78d7c92c5fa102ea77fcd50c2058b00f6e'

app.config["STATIC_URL"] = CDN if not app.config["DEBUG"] else ""
app.config['SECRET_KEY'] = secret_key

@app.template_global()
def static_url(filename):
    return urljoin(app.config["STATIC_URL"], f"staticfiles/{filename}")

@app.route('/robots.txt/')
def robots():
    return send_file('./robots.txt')


@app.route("/sitemap_index.xml")
@app.route("/page-sitemap.xml")
@app.route("/post-sitemap.xml")
@app.route("/sitemap.xml")
def sitemap():
    """
        Route to dynamically generate a sitemap of your website/application.
    """
    us_eastern = pytz.timezone('US/Eastern')
    host_components = urlparse(request.host_url)
    host_base = host_components.scheme + "://" + host_components.netloc

    excluded_urls = ['http://127.0.0.1:5004/favicon.ico', 'http://127.0.0.1:5004/robots.txt',
                     'http://127.0.0.1:5004/', 'http://127.0.0.1:5004/sitemap', 
                     'http://127.0.0.1:5004/post-sitemap.xml', 'http://127.0.0.1:5004/page-sitemap.xml',
                     'http://127.0.0.1:5004/sitemap_index.xml', 'http://127.0.0.1:5004/sitemap.xml']

    static_urls = list()
    fileNames = ['index.html']  # Adding Index File Manually
    root_dir = './templates'
    file_info = {}

    try:
        for rule in app.url_map.iter_rules():
            if "GET" in rule.methods and len(rule.arguments) == 0 and not rule.rule.startswith("/admin") and not rule.rule.startswith("/user"):
                url = {"loc": f"{host_base}{rule}"}
                file_name = rule.rule.strip("/") + '.html'
                fileNames.append(file_name)
                static_urls.append(url)

        for subdir, dirs, files in os.walk(root_dir):
            for filename in files:
                if filename in fileNames:
                     # Extract the key from filename (e.g., 'bundles' from 'bundles.html')
                    file_key = filename[:-5]
                    file_path = os.path.join(subdir, filename)
                    modified_time = os.path.getmtime(file_path)
                    dt = datetime.datetime.fromtimestamp(modified_time, tz=datetime.timezone.utc)
                    dt_us_eastern = dt.astimezone(us_eastern)
                    iso_format = dt_us_eastern.strftime('%Y-%m-%dT%H:%M:%S%z')
                    iso_format = iso_format[:-2] + ":" + iso_format[-2:]
                    file_info[file_key] = iso_format
        
        print(file_info)
        xml_sitemap = render_template("sitemap.xml", static_urls=static_urls, host_base=host_base, 
                                      excluded_urls=excluded_urls, file_info=file_info)
        response = make_response(xml_sitemap)
        response.headers["Content-Type"] = "application/xml"
        return response

    except Exception as e:
        print(f"Error occurred while generating sitemap: {e}")
        # traceback.print_exc()
        return "An error occurred while generating the sitemap", 500
        
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/privacy-policy/')
def privacy():
    return redirect("https://www.viasat.com/privacy/"), 200

@app.route('/contact-us/')
def contact():
    return render_template('contact-us.html')    

# @app.route('/internet')
# def internet():
#     return render_template('internet.html')

# @app.route('/business')
# def business():
#     return render_template('business.html')

# @app.route('/bundles')
# def plans():
#     return render_template('bundles.html')

@app.route('/contact-us/')
def cs():
    return render_template('contact-us.html')


# @app.route('/phone')
# def phone():
#     return render_template('Phone.html')
    
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5010)
