@font-face {
  font-family: "ARS Maquette Pro Light";
  src: url(../staticfiles/ARSMaquettePro-Light.woff);
}

@font-face {
  font-family: "ARS Maquette Pro Regular";
  src: url(../staticfiles/ARSMaquettePro-Regular.woff);
}

@font-face {
  font-family: "ARS Maquette Pro Medium";
  src: url(../staticfiles/ARSMaquettePro-Medium.woff);
}

@font-face {
  font-family: "ARS Maquette Pro Bold";
  src: url(../staticfiles/ARSMaquettePro-Bold.woff);
}
/* Define font family variables */
:root {
  --font-ars-maquette-light: "ARS Maquette Pro Light";
  --font-ars-maquette-regular: "ARS Maquette Pro Regular";
  --font-ars-maquette-medium: "ARS Maquette Pro Medium";
  --font-ars-maquette-bold: "ARS Maquette Pro Bold";
}
.barckground-grey {
  background: #f9f9f9;
}
.question-section {
  position: relative;
}

/* yellow-banner-section */
.why-choose-wow-services {
  padding: 2.125rem 0rem 3.4375rem;
}

.our-services-heading p {
  max-width: 82.375rem;
  margin: 1.375rem auto 0rem;
  line-height: 1.25rem;
  font-family: var(--font-ars-maquette-light);
  font-size: 0.875rem;
}
.our-services-background {
  background-image: url(/staticfiles/why-choose-chodasoo.webp);
  justify-content: center;
  padding: 4.4375rem 0 4.4375rem;
}
.mobile-image {
  display: none;
}

/* slider */
.tab {
  display: none;
}

.slider-container {
  width: 80%;
  margin: 0 auto;
  overflow: hidden;
}

.slider {
  display: flex;
  transition: all 0.5s;
}

.slide {
  flex: 0 0 20%;
  /* Display 5 products per slide */
  padding: 0.625rem;
  box-sizing: border-box;
  border: 0.0625rem solid #ccc;
}

.product {
  background: #f9f9f9;
  padding: 1.25rem;
  text-align: center;
  margin: 0.625rem;
}

/* internet-plans-section */
.best-value {
  border-radius: 0.625rem 0.625rem 0rem 0rem;
  background: var(--color-secondary);
  padding: 0.875rem 1rem;
}
.plans-section .owl-carousel.owl-theme .owl-nav.disabled {
  display: none;
}

.upload-doenload {
  display: flex;
  column-gap: 0.8125rem;
}
.download-speed {
  padding-top: 2.3125rem;
}
.upload-doenload span {
  color: var(--color-black);
  font-family: var(--font-ars-maquette-light);
  font-size: 0.8125rem;
  font-weight: 300;
}
/* faqs */
.faqs-container {
  margin: 0 auto;
  max-width: 79rem;
  padding: 0 1.25rem;
}
.accordion button:hover,
.accordion button:focus {
  cursor: pointer;
  color: var(--color-white);
}

.accordion button:hover::after,
.accordion button:focus::after {
  cursor: pointer;
  color: #000;
  border: 0.0625rem solid #03b5d2;
}
.accordion button .icon::before {
  display: block;
  position: absolute;
  content: "";
  top: 1.0625rem;
  left: 0.625rem;
  width: 1.25rem;
  height: 0.1875rem;
  background-color: #fff;
}

.accordion button .icon::after {
  display: block;
  position: absolute;
  content: "";
  top: 0.625rem;
  left: 1.125rem;
  width: 0.1875rem;
  height: 1.125rem;
  background-color: #fff;
}

.accordion button[aria-expanded="true"] .icon::after {
  width: 0;
}
.plans-section .owl-carousel.owl-theme .owl-nav.disabled {
  display: block;
}
.plans-section .owl-prev span::after {
  background-image: url("/staticfiles/wow-homepage-internet-plan-visa-card-1-slider-arrow-cion.webp");
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 3.75rem;
  height: 3.75rem;
}
.about-services-heading {
  width: 64%;
  margin: 0 auto;
}
.wow-image {
  width: 16%;
}

.question-banner {
  column-gap: 2.5rem;
}

.about-services-heading h2 {
  font-size: 2.875rem;
}

.call-now-button {
  margin: 1.125rem 0rem 2rem;
}

.call-now-button a {
  font-size: 1.5rem;
  width: 25.875rem;
  height: 3.5rem;
}
.main-button {
  margin-top: 1.25rem;
}

.main-button a {
  font-size: 1rem;
  padding: 0.75rem 1.625rem;
}
.about-services-heading h2 {
  font-size: 2.5rem;
}
.question-banner {
  justify-content: center;
  top: 3.125rem;
}
.wow-image {
  margin-top: 1.375rem;
}
.about-services-heading h2 {
  font-size: 1.875rem;
  line-height: 2.5rem;
}

.question-banner {
  top: 4.75rem;
}
.Internet-tv-button .banner-ctaa-icons {
  padding: 0.75rem;
  width: unset;
  height: unset;
}
.our-services-heading p {
  padding: 0 1.25rem;
}
.main-button a {
  font-size: 1rem;
  padding: 0.875rem 0.75rem;
}
.our-services-background {
  height: unset;
}

.mobile-image {
  display: block;
}

.banner-question-image {
  display: none;
}

.question-banner {
  top: 0;
  flex-direction: column;
  column-gap: 0;
  justify-content: unset;
  left: 0;
}

.wow-image {
  width: 48%;
  margin: 0 auto;
}

.about-services-heading h2 {
  text-align: center;
  margin: 0 auto;
  max-width: 23.5625rem;
  line-height: 2.3125rem;
}

.question-banner .about-services-heading h2 br {
  display: none;
}

.call-now-button a {
  font-size: 1.25rem;
  width: 21.25rem;
  height: 3.25rem;
  margin: 0 auto;
}

.call-now-button a img {
  width: 1.5625rem;
  height: 1.5625rem;
  padding-right: 0.9375rem;
}

.wow-banner-images {
  flex-direction: column;
}

.wow-banner-image-1 img {
  max-width: 20%;
}

.wow-banner-images {
  flex-direction: column;
  align-items: self-start;
}

.wow-banner-image-1 img {
  max-width: 78%;
}

.wow-banner-image-1 {
  text-align: center;
}
.question-banner {
  padding-top: 1.5rem;
}
.plans-section .owl-prev span::after {
  left: -3.8125rem;
  transform: scale(0.7);
}
