<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="../staticfiles/phone.css">
    <link href="https:/staticfiles/.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
</head>

<body>
    <main class="all-main">
        <!-- banner-section -->
        <section class="banner-section phone-section">
            <div class="banner-background">
                <div class="hughsenet-section">
                    <div class="hughsenet-heading">
                        <h1><label>HughesNet® Home Phone Service</label></br><span>America’s #1 Choice for Satellite
                                Internet</span> brings the Perfect Voice Plans for You.</h1>
                        <p>Wherever you live you cannot only get internet access, but a great phone service too.
                            HughesNet Voice offers the quality and features you want―at a price you’ll love―without
                            using any of your service plan data. Wherever you live you cannot only get internet access,
                            but a great phone service too. HughesNet Voice offers the quality and features you want―at a
                            price you’ll love―without using any of your service plan data.
                        </p>
                        <div class="main-button">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-dollar-icon.webp" alt="">Go to
                                Plans</a>
                            <a href=tel:********** class="call-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-phone-icon.webp" alt="">Call to
                                Order</a>
                        </div>
                    </div>
                    <div class="hughsenet-heading">

                    </div>
                </div>
            </div>
        </section>

        <!-- online-fast-section -->

        <section class="online-fast-section">
            <div class="online-fast-image">
                <div class="all-online-fast">
                    <div class="online-fast-heading">
                        <h2>Do All the Things You Do Online, Fast!</h2>
                        <h3>Call to Order Now & Enjoy Free Professional Installation*</h3>
                    </div>
                    <div class="online-fast-heading-cta">
                        <div class="arrow-container">
                            <img src="../staticfiles/hughesnet-home-online-fast-image-arrow.webp" alt="Arrow"
                                class="arrow">
                        </div>
                        <div class="all-online-buttn">
                            <a href=tel:**********><img src="../staticfiles/hughsnet-home-page-banner-phone-icon.webp"
                                    alt="">************</a>
                        </div>
                    </div>
                </div>
                <div class="free-paragraph">
                    <p>*Free standard professional installation applies to new Lease subscribers only. Not valid
                        with Purchase option. Limited-time offer.</p>
                </div>
            </div>
        </section>
        <!-- hughsnet-plan-section -->

        <section class="hughsnet-plan-section phone-page-hughsnet">
            <div class="hughsnet-plan-heading">
                <h2><span>HughesNet®</span> - Phone Service Plans & Pricing</h2>
                <h3>An Enhanced Experience of Satellite Internet</h3>
                <p>HughesNet Voice for your home is an optional service which comes with the features you want, at a
                    great price. Whether you opt for a HughesNet satellite-only or a Fusion service plan, you can enjoy
                    more value and convenience when you order both at the same time</p>
            </div>
            <div class="hughsnet-plan-background">
                <div class="hughsnet-cards">
                    <div class="hughsnet-card1">
                        <div class="starter-plan">
                            <h3>HughesNet Voice</br><span>Domestic US Calling Plans</span></h3>
                        </div>
                        <div class="month-plan">
                            <div class="month-plan-prices">
                                <h3>$49.99</h3>
                                <h3>/month</h3>
                            </div>
                            <div class="month-plan-prices">
                                <h3>30<sup>GB</sup></h3>
                                <h3>DATA</h3>
                            </div>
                        </div>
                        <div class="hughsenet-heading-list plans-listing">
                            <h3><span><img src="../staticfiles/hughsenet-home-page-hughsnet-plan-enjoy-icon.webp"
                                        alt=""></span><span>Enjoy blazing fast speeds up to 1 Gig</span></h3>
                            <h3><span><img src="../staticfiles/hughsenet-home-page-hughsnet-plan-play-icon.webp"
                                        alt=""></span><span>Stream HD videos, play games, shop online and do so much
                                    more</span>
                            </h3>
                            <h3><span><img src="../staticfiles/hughsenet-home-page-hughsnet-plan-secure-icon.webp"
                                        alt=""></span><span>Secure your devices, data and network for a safer web
                                    surfing</span></h3>
                        </div>
                        <div class="main-button plan-button">
                            <a href=tel:********** class="call-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-phone-icon.webp" alt="">Call to
                                Order</a>
                        </div>
                    </div>
                    <div class="hughsnet-card1">
                        <div class="starter-plan">
                            <h3>HughesNet Voice</br><span>International Calling Plans</span></h3>
                        </div>
                        <div class="month-plan">
                            <div class="month-plan-prices">
                                <h3>$149.99</h3>
                                <h3>/month</h3>
                            </div>
                            <div class="month-plan-prices">
                                <h3>150<sup>GB</sup></h3>
                                <h3>DATA</h3>
                            </div>
                        </div>
                        <div class="hughsenet-heading-list plans-listing">
                            <h3><span><img src="../staticfiles/hughsenet-home-page-hughsnet-plan-enjoy-icon.webp"
                                        alt=""></span><span>Enjoy blazing fast speeds up to 1 Gig</span></h3>
                            <h3><span><img src="../staticfiles/hughsenet-home-page-hughsnet-plan-play-icon.webp"
                                        alt=""></span><span>Stream HD videos, play games, shop online and do so much
                                    more</span>
                            </h3>
                            <h3><span><img src="../staticfiles/hughsenet-home-page-hughsnet-plan-secure-icon.webp"
                                        alt=""></span><span>Secure your devices, data and network for a safer web
                                    surfing</span></h3>
                        </div>
                        <div class="main-button plan-button">
                            <a href=tel:********** class="call-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-phone-icon.webp" alt="">Call to
                                Order</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="plan-bottom-lines">
                <p>*Actual speeds may vary and are not guaranteed.</p>
            </div>
        </section>

        <!-- break through section -->

        <section class="at-a-glance">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading">
                        <h2><span>HugesNet® Voice</span> - Break through Technology That Works</h2>
                        <p>HughesNet Voice uses advanced Voice-over-IP (VoIP) technology to deliver a feature-rich home
                            phone service over your satellite connection. New HughesNet customers have the Analog
                            Telephone Adapter (ATA) set up by the installer that routes your calls through your
                            satellite modem and dish. It’s as easy as that! and the wonderful thing is HughesNet Voice
                            does not use data from your service plan! your calls through your satellite modem and dish.
                            It’s as easy as that! and the wonderful thing is HughesNet Voice does not use data from your
                            service plan!</p>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                    <div class="glace-heading-image">
                        <img src="../staticfiles/hughsnet-phone-page-break-image.webp" alt="">
                    </div>
                </div>
            </div>
            </div>
        </section>

        <!-- keep-current-section -->

        <section class="at-a-glance keep-current">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading-image">
                        <img src="../staticfiles/hughsnet-phone-page-crunnet-number-image.webp" alt="">
                    </div>
                    <div class="glace-heading">
                        <h2>Keep Your Current Number with <span>HughesNet® Voice</span></h2>
                        <p>With HughesNet Voice you can keep your current phone number if you already own a local
                            number. All you’ve to do is at the time you register for HughesNet Voice, select a new
                            telephone number or transfer an existing preferred number to your HughesNet Voice account
                            via porting. There is no additional charge.select a new telephone number or transfer an
                            existing preferred number to your HughesNet Voice account via porting. There is no
                            additional charge.</p>
                        <p>*Transfers of an existing telephone number are not always available.</p>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </section>

        <!-- internet sufring section -->

        <section class="at-a-glance">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading">
                        <h2><span>HugesNet® Voice</span> - Popular Calling Features for FREE</h2>
                        <p>Many useful calling features that make everyday communication hassle-free, come included with
                            the HughesNet phone service for your home. Call Waiting, Caller ID, Call Forwarding, Call
                            Block, Simultaneous Ring etc. Enhanced Voicemail (accessible via dial-in and forwarded via
                            email) The web Self-Care Portal for ready control of your Voice serviceSimultaneous Ring
                            etc. Enhanced Voicemail (accessible via dial-in and forwarded via email) The web Self-Care
                            Portal for ready control of your Voice service.</p>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                    <div class="glace-heading-image">
                        <img src="../staticfiles/hughsnet-phone-page-popular-call-image.webp" alt="">
                    </div>
                </div>
            </div>
            </div>
        </section>

        <!-- more-affordable-section -->

        <section class="at-a-glance keep-current">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading-image">
                        <img src="../staticfiles/hughsnet-phone-page-more-affordable-image.webp" alt="">
                    </div>
                    <div class="glace-heading">
                        <h2><span>HughesNet® Voice</span> Is More Affordable</h2>
                        <p>HughesNet Voice can be bundled with any Internet service plan―including the new Fusion plans.
                            HughesNet phone plans are so affordable you can save up to 45% of the cost you’d incur with
                            a typical home phone service. Apart from unlimited nationwide calling, there are add-on
                            international calling plans that come at a flat monthly rate, helping you save all around.
                            Apart from unlimited nationwide calling, there are add-on international calling plans that
                            come at a flat monthly rate, helping you save all around. Apart from unlimited nationwide
                            calling, there are add-on international calling plans that come at a flat monthly rate,
                            helping you save all around.</p>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </section>

        <!-- plan-have-a -->

        <section class="at-a-glance lot-more-value data-saving-feacture">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading">
                        <h2><span>HughesNet®</span> - Plans Have a Lot More Value!</h2>
                        <div class="inter-sufring-listing">
                            <h3>Data-Saving Features</h3>
                            <p>
                                HughesNet includes data-saving features so you can
                                get the most out of your service―</p>
                            <ul>
                                <li>The service automatically adjusts data rates for streaming video to deliver
                                    great picture quality while using less of your data―and you get to watch
                                    3x more videos.*</li>
                                <li>HughesNet also automatically compresses and optimizes web content with
                                    built-in SmartTechnologies to make web pages load faster while using less data</li>
                                <li>The feature is designed to let you opt out of the option permanently or snooze
                                    it for 4 hours</li>
                            </ul>
                        </div>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                    <div class="glace-heading-image">
                    </div>
                </div>
            </div>
            </div>
        </section>

        <!-- Faqs-section -->
        <section class="Faqs-section">
            <div class="faqs-container">
                <h2>Frequently Asked Questions (FAQs)</h2>
                <div class="accordion">
                    <div class="accordion-item">
                        <button id="accordion-button-1" aria-expanded="false">
                            <span class="accordion-title">Is <strong>HughesNet</strong> available in my Area?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-2" aria-expanded="false">
                            <span class="accordion-title">Why is the sky blue?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-3" aria-expanded="false">
                            <span class="accordion-title">Will we ever discover aliens?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-4" aria-expanded="false">
                            <span class="accordion-title">How much does the Earth weigh?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-5" aria-expanded="false">
                            <span class="accordion-title">How do airplanes stay up?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-6" aria-expanded="false">
                            <span class="accordion-title">How do airplanes stay up?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-7" aria-expanded="false">
                            <span class="accordion-title">How do airplanes stay up?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-8" aria-expanded="false">
                            <span class="accordion-title">How do airplanes stay up?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        const items = document.querySelectorAll('.accordion button');

        function toggleAccordion() {
            const itemToggle = this.getAttribute('aria-expanded');

            for (i = 0; i < items.length; i++) {
                items[i].setAttribute('aria-expanded', 'false');
            }

            if (itemToggle == 'false') {
                this.setAttribute('aria-expanded', 'false');
            }
        }

        items.forEach((item) => item.addEventListener('click', toggleAccordion));




    </script>
</body>

</html>