<header class="header-area" id="myHeader">
  <!-- site-navbar start -->
  <div class="navbar-area">
    <div class="container">
      <nav class="site-navbar">
        <!-- site menu/nav -->
        <div class="menu" id="menu">
          <div class="logo">
            <a href="/"><img src="/staticfiles/viasat-header-logo.webp" alt="" /></a>
          </div>
          <ul>
            <li> <a href="/">Home</a></li>
            <li> <a href="/contact-us/">Contact Us</a></li>
          </ul>
        </div>
        <div class="menu hire-developer-btn">
          <a href="tel:8552430017" class="hire-dev"><img src="/staticfiles/viasat-banner-call-icon.webp" alt="">Call Now
            | ************</a>
          <img class="mobile-big-logo" src="/staticfiles/header-right-logo.svg" alt="">
        </div>
        </ul>

        <!-- nav-toggler for mobile version only -->
        <button class="nav-toggler">
          <span></span>
        </button>
      </nav>
    </div>
  </div>

  <script>
    ///header
    // define all UI variable
    const navToggler = document.querySelector(".nav-toggler");
    const navMenu = document.querySelector(".site-navbar ul");
    const navLinks = document.querySelectorAll(".site-navbar a");

    // load all event listners
    allEventListners();

    // functions of all event listners
    function allEventListners() {
      // toggler icon click event
      navToggler.addEventListener("click", togglerClick);
      // nav links click event
      navLinks.forEach((elem) => elem.addEventListener("click", navLinkClick));
    }

    // togglerClick function
    function togglerClick() {
      navToggler.classList.toggle("toggler-open");
      navMenu.classList.toggle("open");
    }

    // navLinkClick function
    function navLinkClick() {
      if (navMenu.classList.contains("open")) {
        navToggler.click();
      }
    }
  </script>
  <script>


    // 	sticky
    // JavaScript to handle the sticky behavior
    window.onscroll = function () {
      makeHeaderSticky();
    };

    var header = document.getElementById("myHeader");
    var sticky = header.offsetTop + 50;

    function makeHeaderSticky() {
      if (window.pageYOffset > sticky) {
        header.classList.add("sticky");
      } else {
        header.classList.remove("sticky");
      }
    }		
  </script>
</header>
<script>
  const burger = document.getElementById("burger");
  const menu = document.getElementById("menu");

  burger.addEventListener("click", () => {
    menu.classList.toggle("active");
  });
</script>