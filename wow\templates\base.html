<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    {% block indexTag %} {% if include_indexTag|default(true) %}
    <meta
      name="robots"
      content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"
    />

    {% else %}

    <meta
      name="robots"
      content="noindex, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"
    />
    {% endif %} {% endblock indexTag %}
    <link rel="shortcut icon" href="/staticfiles/wow-favicon.ico" type="image/x-icon" />
    <meta name="language" content="English" />

    <link
      rel="preload"
      href="https://d2s3ce92b3j7xq.cloudfront.net/providers-websites/ARSMaquettePro-Bold-subset.woff2"
      as="font"
      type="font/woff2"
      crossorigin="anonymous"
    />

    <link
      rel="preload"
      href="https://d2s3ce92b3j7xq.cloudfront.net/providers-websites/ARSMaquettePro-Regular-subset.woff2"
      as="font"
      type="font/woff2"
      crossorigin="anonymous"
    />

    <link
      rel="preload"
      href="https://d2s3ce92b3j7xq.cloudfront.net/providers-websites/ARSMaquettePro-Medium-subset.woff2"
      as="font"
      type="font/woff2"
      crossorigin="anonymous"
    />

    <link
      rel="preload"
      href="https://d2s3ce92b3j7xq.cloudfront.net/providers-websites/ARSMaquettePro-Light-subset.woff2"
      as="font"
      type="font/woff2"
      crossorigin="anonymous"
    />

    <style nonce="{{nonce}}">
      @font-face {
        font-family: "ARS Maquette Pro Light";
        src: url("https://d2s3ce92b3j7xq.cloudfront.net/providers-websites/ARSMaquettePro-Light-subset.woff2")
          format("woff2");
        font-weight: 300;
        font-style: normal;
      }

      @font-face {
        font-family: "ARS Maquette Pro Regular";
        src: url("https://d2s3ce92b3j7xq.cloudfront.net/providers-websites/ARSMaquettePro-Regular-subset.woff2")
          format("woff2");
        font-weight: 400;
        font-style: normal;
      }

      @font-face {
        font-family: "ARS Maquette Pro Medium";
        src: url("https://d2s3ce92b3j7xq.cloudfront.net/providers-websites/ARSMaquettePro-Medium-subset.woff2")
          format("woff2");
        font-weight: 500;
        font-style: normal;
      }

      @font-face {
        font-family: "ARS Maquette Pro Bold";
        src: url("https://d2s3ce92b3j7xq.cloudfront.net/providers-websites/ARSMaquettePro-Bold-subset.woff2")
          format("woff2");
        font-weight: 700;
        font-style: normal;
      }

      /* Define font family variables */
      :root {
        --font-ars-maquette-light: "ARS Maquette Pro Light";
        --font-ars-maquette-regular: "ARS Maquette Pro Regular";
        --font-ars-maquette-medium: "ARS Maquette Pro Medium";
        --font-ars-maquette-bold: "ARS Maquette Pro Bold";
      }

      body,
      p {
        font-family: var(--font-ars-maquette-regular), sans-serif;
      }
    </style>

    <link rel="stylesheet" href="../staticfiles/header.css" />
    <link rel="stylesheet" href="../staticfiles/footer.css" />
    {% if g.MOBILE or request.TABLET %}
    <!-- mobile here -->
    <link rel="stylesheet" href="../staticfiles/style.css" />
    {% else %}
    <!-- desktop here -->
    <link rel="stylesheet" href="../staticfiles/style.css" />
    {% endif %}
    <!-- Google Tag Manager -->

    <script
      nonce="{{nonce}}"
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-C22SE5ESJY"
      defer
    ></script>

    <script nonce="{{ nonce }}" defer>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-C22SE5ESJY");
    </script>

    <!-- End Google Tag Manager -->
    {% block header_css_file %}{% endblock %}
  </head>

  <body>
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-53Q8N9QH"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>

    <!-- End Google Tag Manager (noscript) -->
    {%include 'includes/navbar.html'%}
    <section class="content">
      <header>{% block header %}{% endblock %}</header>
      {% for message in get_flashed_messages() %}
      <div class="flash">{{ message }}</div>
      {% endfor %} {% block content %}{% endblock %}
    </section>
    {%include 'includes/footer.html'%}
  </body>
</html>
