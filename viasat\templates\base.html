<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    {% block indexTag %} {% if include_indexTag|default(true) %}
    <meta
      name="robots"
      content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"
    />

    {% else %}

    <meta
      name="robots"
      content="noindex, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"
    />
    {% endif %} {% endblock indexTag %}
    <link rel="icon" href="/staticfiles/viasat-favicon.ico" type="image/x-icon" />
    <meta name="language" content="English" />
    <link rel="stylesheet" href=" {{ static_url(filename='header.css')}} " />
    <link rel="stylesheet" href=" {{ static_url(filename='footer.css')}} " />
    <link rel="stylesheet" href=" {{ static_url(filename='style.css')}} " />
    <script defer>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-KDLW6WWN");
    </script>
    {% block header_css_file %}{% endblock %}
  </head>

  <body>
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-KDLW6WWN"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    {%include 'includes/navbar.html'%}
    <section class="content">
      <header>{% block header %}{% endblock %}</header>
      {% for message in get_flashed_messages() %}
      <div class="flash">{{ message }}</div>
      {% endfor %} {% block content %}{% endblock %}
    </section>
    {%include 'includes/footer.html'%}
  </body>
</html>
