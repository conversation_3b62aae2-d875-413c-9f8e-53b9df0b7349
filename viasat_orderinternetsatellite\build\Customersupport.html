<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="../staticfiles/customersupport.css">
    <link href="https:/staticfiles/.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
</head>

<body>
    <main class="all-main">
        <!-- banner-section -->
        <section class="banner-section customersupport-banner">
            <div class="banner-background">
                <div class="hughsenet-section">
                    <div class="hughsenet-heading">
                        <h1><label>HughesNet® </label>Customer Service is Just a Call Away!</h1>
                        <h2>Get Instant Tech Support</h2>
                        <div class="hughsnet-phone-number">
                            <p>HughesNet Phone Number</p>
                            <a href=tel:**********>**************</a>
                        </div>
                        <div class="hughsnet-phone-number phone-espanol">
                            <p>HughesNet Servicio al cliente En Español</p>
                            <a href=tel:**********>**************</a>
                        </div>
                        <div class="main-button">
                            <a href=tel:********** class="call-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-phone-icon.webp" alt="">Call to
                                Order</a>
                        </div>
                    </div>
                    <div class="hughsenet-heading">

                    </div>
                </div>
            </div>
        </section>

        <!-- Ways-to-Contact -->

        <section class="Ways-to-Contact">
            <div class="Ways-to-Contact-boxes">
                <div class="Ways-to-Contact-box-1">
                    <h2>Ways to Contact <span>HughesNet® Customer Service</span></h2>
                    <img src="../staticfiles/customer-support-page-way-to-contact-image.webp" alt="">
                    <p>HughesNet cares about its subscriber-base, and aims to come forward with convenient to access
                        support. That’s why getting in touch with HughesNet Customer Service is not restricted to a
                        phone call alone. You can make use of any of the following channels to get support with
                        service-related concerns, billing questions, technical issues, and more! So, if you are not in
                        the mood to call the HughesNet customer service number, choose the channel that is the most
                        convenient, and get in touch with a HughesNet representative to talk to!. get in touch with a
                        HughesNet representative to talk to!.</p>
                    <div class="main-button">
                        <a href=tel:********** class="dollar-icon"><img
                                src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                            to Order</a>
                    </div>
                </div>
                <div class="Ways-to-Contact-box-2">
                    <div class="hughsnet-phone-call">
                        <div class="husghsnet-phone-call1">
                            <div class="phone-call-heading-hughsnet">
                                <h4><img src="../staticfiles/hughsnet-customer-page-way-contract-icon.webp" alt="">
                                </h4>
                                <h4><span>HughesNet®</span></br>
                                    Phone Call</h4>
                            </div>
                            <p>Talk to a well-trained representative and get answers to all your questions through a
                                phone call. ************** for HughesNet customer support. enter the relevant code, and
                                get routed to the right department for your concerned cause. customer support. enter the
                                relevant cod. for your concerned cause. customer support. enter the relevant cod</p>
                        </div>
                        <div class="husghsnet-phone-call1">
                            <div class="phone-call-heading-hughsnet">
                                <h4><img src="../staticfiles/hughsnet-customer-page-way-contract-icon1.webp" alt="">
                                </h4>
                                <h4><span>HughesNet®</span></br>
                                    Phone Call</h4>
                            </div>
                            <p>You can see the status of your most recent repair order by filling the online inquiry
                                form. In the form, you will have to mention your primary phone number, zip code, and SAN
                                or account number. After filling out the form, click on ‘View Order Status’ and you will
                                be presented with all the details regarding your most recent order.</p>
                        </div>
                    </div>
                    <div class="hughsnet-phone-call">
                        <div class="husghsnet-phone-call1">
                            <div class="phone-call-heading-hughsnet">
                                <h4><img src="../staticfiles/hughsnet-customer-page-way-contract-icon2.webp" alt="">
                                </h4>
                                <h4><span>HughesNet®</span></br>
                                    Phone Call</h4>
                            </div>
                            <p>Having trouble connecting through live chat? Fret not. Simply send an email and HughesNet
                                will get back to you within 24 hours. You can also fill the online email support form,
                                which requires you to state your first and last name, account number, email ID, and the
                                issue you are facing with your connection.</p>
                        </div>
                        <div class="husghsnet-phone-call1">
                            <div class="phone-call-heading-hughsnet">
                                <h4><img src="../staticfiles/hughsnet-customer-page-way-contract-icon3.webp" alt="">
                                </h4>
                                <h4><span>HughesNet®</span></br>
                                    Phone Call</h4>
                            </div>
                            <p>HughesNet actively maintains its social media accounts including
                                twitter.com AskHughes and www.facebook.com HughesNet in order to convey
                                its messages regarding special offers and promotions to the customers. You can also
                                approach them to lodge complaints regarding your internet connection.</p>
                        </div>
                    </div>
                    <div class="hughsnet-phone-call">
                        <div class="husghsnet-phone-call1">
                            <div class="phone-call-heading-hughsnet">
                                <h4><img src="../staticfiles/hughsnet-customer-page-way-contract-icon4.webp" alt="">
                                </h4>
                                <h4><span>HughesNet®</span></br>
                                    Phone Call</h4>
                            </div>
                            <p>If you do not feel like talking and want to skip the customer support queue on the phone,
                                then simply get help from HughesNet customer service chat. To use this feature, keep
                                your HughesNet account number handy as you will be needing it when talking to the
                                representative.handy as you will be needing it when talking to the representative.</p>
                        </div>
                        <div class="husghsnet-phone-call1">
                            <div class="phone-call-heading-hughsnet">
                                <h4><img src="../staticfiles/hughsnet-customer-page-way-contract-icon5.webp" alt="">
                                </h4>
                                <h4><span>HughesNet®</span></br>
                                    Phone Call</h4>
                            </div>
                            <p>HughesNet lists different help topics on its Community Support page so that customers can
                                easily locate their concerned subject and find instant solution. If you are unable to
                                find your desired category then post a question, which will be publically available for
                                everyone to see including HughesNet customer service. You can also share your ideas and
                                feedback on the community page.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Customer services section -->

        <section class="at-a-glance">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading">
                        <h2><span>HughesNet®</span> Customer Service Understands You!</h2>
                        <p>HughesNet is a great choice not only in terms of consumer products and services but
                            after-sales customer experience too. Committed to offering reliable satellite internet
                            service across the United States for over 20 years – initially under the brand name DirecPC,
                            and from 2012 onwards under the new brand name HughesNet® - the provider has come to fully
                            grasp the needs of its consumer-base. Not only does HughesNet ensure to make the ordering
                            process hassle-free for you, but you will find them dynamic when it comes to serving
                            subscribers after the fact. So if and when, there is an interruption in the service -
                            something that is inevitable at times - and you find yourself facing unstable or poor
                            internet performance or a disruption in the HughesNet phone service, many a channel is at
                            your disposal to help you reach out to HughesNet Customer Service. You will find them always
                            striving to live up to your expectations!</p>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                    <div class="glace-heading-image">
                        <img src="../staticfiles/hughsnet-customersupport-understand-image.webp" alt="">
                    </div>
                </div>
            </div>
            </div>
        </section>

        <!-- need-technical-help -->
        <section class="at-a-glance need-help-techinacal">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading-image">
                        <img src="../staticfiles/hughsnet-customersupport-techinacal-help-image.webp" alt="">
                    </div>
                    <div class="glace-heading">
                        <h2>Need Technical Help?<span> HughesNet® </span>Technical Support Is Here to the Rescue!
                        </h2>
                        <p>Are you seeking professional help with a technical issue? If yes, know that HughesNet
                            Technical Support has your back! For any kind of connection problems or technical issues,
                            you can dial in to 866-347-3292 and find a resolution to service related inquiries. Maybe
                            you have just subscribed to HughesNet Internet and must schedule an installation
                            appointment, one of the equipment pieces malfunctioned and you need to check on the repair
                            order, you need troubleshooting tips to resolve a minor glitch in you in-home network, or
                            you need assistance with ordering new equipment. You can reach out to Hughes Internet
                            Customer Service and request technical support.</p>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                </div>
            </div>

        </section>

        <!-- business subscprition -->
        <section class="at-a-glance need-help-techinacal1">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading">
                        <h2>Get Support for<span> HughesNet® </span>Business Subscriptions
                        </h2>
                        <p>If you have already subscribed to HughesNet Business plans, you can dial in to the Business
                            Support line at ************.
                            From general to technical inquiries, from purchasing new HughesNet equipment to checking the
                            status of a repair request, from immediate assistance with service related problems to
                            learning how to troubleshoot, and from billing concerns to questions about account status -
                            you can contact the HughesNet’s Business Support number! HughesNet customer service
                            representatives dedicated to HughesNet’s business clientele will assist you, guide you, and
                            lead you to a prompt and satisfactory resolution to any question or concern. HughesNet
                            customer service representatives dedicated to HughesNet’s business clientele will assist
                            you, guide you, and lead you to a prompt and satisfactory resolution to any question or
                            concern.</p>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                    <div class="glace-heading-image">
                        <img src="../staticfiles/hughsnet-customersupport-business-subscprition-image.webp" alt="">
                    </div>
                </div>
            </div>

        </section>

        <!-- before-call-section -->
        <section class="at-a-glance need-help-techinacal">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading-image">
                        <img src="../staticfiles/hughsnet-customersupport-before-you-call-image.webp" alt="">
                    </div>
                    <div class="glace-heading">
                        <h2><span>HughesNet®</span> - Here Is What You Need to Do Before You Call</h2>
                        <p>Before you make a call, there are a few things you need to have on hand so that you don’t
                            miss out on any important detail. We recommend you take a look at what you must be prepared
                            with before you dial in</p>
                        <div class="inter-sufring-listing">
                            <ul>
                                <li>Your HughesNet account number If you have a recent paper bill, you will be able to
                                    find your account number printed on it. You can also locate it from email
                                    correspondence following your subscription.</li>
                                <li>Your billing address If you are planning to make a move and move HughesNet services
                                    along, chances are you will be asked about your billing address. Make sure you write
                                    down the exact address so the representative can assist you promptly.</li>
                                <li>The model & make of your router In case you find yourself facing connectivity
                                    issues, ensure you take down equipment details to pass onto the representative once
                                    you are connected.</li>
                            </ul>
                        </div>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                </div>
            </div>

        </section>

        <!-- paying-your -->
        <section class="at-a-glance need-help-techinacal1">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading">
                        <h2>Viewing and Paying Your <span>HughesNet®</span> Bill Is Easier Than You Think </h2>
                        <p>When you subscribe to HughesNet, you will be required to provide your credit or debit card
                            details that are subsequently used to automatically process monthly billed dues. To view
                            your bill or change your payment details, all you have to do is sign into your HughesNet
                            account.If you face a challenge while processing your payment, get in touch with HughesNet
                            directly to make a payment over the phone, and avoid the unnecessary hassle of incurring a
                            late fee or service termination.So whether you are stuck with how to pay your monthly dues
                            or you have questions about your bill, in either case call this HughesNet customer service
                            number for help: ************ When you dial this number, you will be connected to an expert
                            agent who’ll assist you with:</p>
                        <div class="inter-sufring-listing">
                            <ul>
                                <li>Checking on the payment date.</li>
                                <li>Switching an automatic payment to another payment method.</li>
                                <li>Making a payment over the phone.</li>
                            </ul>
                        </div>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                    <div class="glace-heading-image">
                        <img src="../staticfiles/hughsnet-customersupport-paying-your-image.webp" alt="">
                    </div>
                </div>
            </div>

        </section>

        <section class="at-a-glance lot-more-value customer-page-data-saving-feacture">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading">
                        <h2><span>HughesNet®</span> - Plans Have a Lot More Value!</h2>
                        <div class="inter-sufring-listing">
                            <h3>Data-Saving Features</h3>
                            <p>
                                HughesNet includes data-saving features so you can
                                get the most out of your service―</p>
                            <ul>
                                <li>The service automatically adjusts data rates for streaming video to deliver
                                    great picture quality while using less of your data―and you get to watch
                                    3x more videos.*</li>
                                <li>HughesNet also automatically compresses and optimizes web content with
                                    built-in SmartTechnologies to make web pages load faster while using less data</li>
                                <li>The feature is designed to let you opt out of the option permanently or snooze
                                    it for 4 hours</li>
                            </ul>
                        </div>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:********** class="dollar-icon"><img src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                    <div class="glace-heading-image">
                    </div>
                </div>
            </div>
            
        </section>

        <!-- Faqs-section -->
        <section class="Faqs-section">
            <div class="faqs-container">
                <h2>Frequently Asked Questions (FAQs)</h2>
                <div class="accordion">
                    <div class="accordion-item">
                        <button id="accordion-button-1" aria-expanded="false">
                            <span class="accordion-title">Is <strong>HughesNet</strong> available in my Area?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-2" aria-expanded="false">
                            <span class="accordion-title">Why is the sky blue?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-3" aria-expanded="false">
                            <span class="accordion-title">Will we ever discover aliens?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-4" aria-expanded="false">
                            <span class="accordion-title">How much does the Earth weigh?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-5" aria-expanded="false">
                            <span class="accordion-title">How do airplanes stay up?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-6" aria-expanded="false">
                            <span class="accordion-title">How do airplanes stay up?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-7" aria-expanded="false">
                            <span class="accordion-title">How do airplanes stay up?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <button id="accordion-button-8" aria-expanded="false">
                            <span class="accordion-title">How do airplanes stay up?</span>
                            <span class="icon" aria-hidden="true"></span>
                        </button>
                        <div class="accordion-content">
                            <p>
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                                incididunt ut labore et dolore magna aliqua. Elementum sagittis vitae et leo duis ut.
                                Ut tortor pretium viverra suspendisse potenti.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        const items = document.querySelectorAll('.accordion button');

        function toggleAccordion() {
            const itemToggle = this.getAttribute('aria-expanded');

            for (i = 0; i < items.length; i++) {
                items[i].setAttribute('aria-expanded', 'false');
            }

            if (itemToggle == 'false') {
                this.setAttribute('aria-expanded', 'false');
            }
        }

        items.forEach((item) => item.addEventListener('click', toggleAccordion));




    </script>
</body>

</html>