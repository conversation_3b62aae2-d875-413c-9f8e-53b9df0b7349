import traceback
from flask import Flask, redirect, render_template, make_response, request, send_file, url_for
from flask_cors import CORS
from urllib.parse import urlparse, urljoin
import datetime, pytz, os

# from flask_frozen import Freezer

app = Flask(__name__, static_folder='staticfiles')
# app.config['FREEZER_DESTINATION'] = 'build'
# freezer = Freezer(app)

allowed_origins = [
    "http://127.0.0.1:8000",  # Replace with your same origin URL
    "https://d3rho3py3wd5ig.cloudfront.net",
    "https://widget-v4.tidiochat.com",
    "https://*.googletagmanager.com",
    "https://*.google-analytics.com",
    "https://*.analytics.google.com",
    "https://*.g.doubleclick.net",
    "https://*.google.com",
    "https://*.google.com.pk",
    "https://www.googleadservices.com",
    "https://googleads.g.doubleclick.net",
    "https://bid.g.doubleclick.net",
    "https://cdnjs.cloudflare.com/ajax/libs/*",
    "http://127.0.0.1:5000"

]

CORS(app, origins = allowed_origins, methods=["GET"])
app.config['CORS_HEADERS'] = 'Content-Type'

# CDN = "https:/d3rho3py3wd5ig.cloudfront.net/"
CDN = "/"

secret_key = '5accdb11b2c10a78d7c92c5fa102ea77fcd50c2058b00f6e'

app.config["STATIC_URL"] = CDN if not app.config["DEBUG"] else ""
app.config['SECRET_KEY'] = secret_key

@app.template_global()
def static_url(filename):
    return urljoin(app.config["STATIC_URL"], f"staticfiles/{filename}")

@app.route('/robots.txt/')
def robots():
    return send_file('./robots.txt')


@app.route("/sitemap_index.xml")
@app.route("/page-sitemap.xml")
@app.route("/post-sitemap.xml")
@app.route("/sitemap.xml")
def sitemap():
    """
        Route to dynamically generate a sitemap of your website/application.
    """
    us_eastern = pytz.timezone('US/Eastern')
    host_base = request.url_root.rstrip('/')  # Ensure no trailing slash

    # Define excluded URLs uniformly
    excluded_urls = set([
        host_base + path for path in [
            '/favicon.ico', 
            '/robots.txt',
            '/sitemap',
            '/post-sitemap.xml',
            '/page-sitemap.xml',
            '/sitemap_index.xml',
            '/sitemap.xml',
            '/blog-inner', 
            '/blog-list', 
            '/create-category',
            '/create-author',
            '/create-tag', 
            '/plogin',
            '/blog',
            '/blog-post'
        ]
    ])
    print("Excluded URLs:", excluded_urls)

    static_urls = []
    fileNames = ['index.html']  # Adding Index File Manually
    root_dir = './templates'
    file_info = {'index': 'default_date_if_missing'}
    blogs_arr= []

    try:
        # Generate URLs and check against exclusions
        for rule in app.url_map.iter_rules():
            if "GET" in rule.methods and len(rule.arguments) == 0 and not any(rule.rule.startswith(x) for x in ["/admin", "/user"]):
                url = f"{host_base}{rule.rule}".rstrip('/')
                file_name = rule.rule.strip("/").replace('/', '-') + '.html'
                fileNames.append(file_name)
                if url not in excluded_urls:
                    static_urls.append({"loc": url})
                else:
                    print('Excluded URL:', url)
                    
        if host_base in static_urls:
            static_urls.remove(host_base)

        # Gather file modification times
        for subdir, dirs, files in os.walk(root_dir):
            for filename in files:
                if filename in fileNames:
                    file_key = filename[:-5]
                    file_path = os.path.join(subdir, filename)
                    modified_time = os.path.getmtime(file_path)
                    dt = datetime.datetime.fromtimestamp(modified_time, tz=datetime.timezone.utc)
                    dt_us_eastern = dt.astimezone(us_eastern)
                    iso_format = dt_us_eastern.strftime('%Y-%m-%dT%H:%M:%S%z')
                    iso_format = iso_format[:-4] + '04:00'
                    file_info[file_key] = iso_format
        
        # results = db.session.query(Blog.permalink, Blog.created_on)
        # for permalink, created_on in results:
        #     created_on_temp = str(created_on)
        #     created_on_temp = created_on_temp.replace(' ','T')
        #     # created_on_temp = created_on_temp[:-3] + '-04:00' -> removes seconds
        #     created_on_temp = created_on_temp + '-04:00'
        #     blogs_arr.append({'permalink': permalink, 'last_mod':created_on_temp})
        


        # Render sitemap XML
        xml_sitemap = render_template("sitemap.xml", static_urls=static_urls, host_base=host_base, 
                                      excluded_urls=excluded_urls, blogs=blogs_arr, file_info=file_info)
        response = make_response(xml_sitemap)
        response.headers["Content-Type"] = "application/xml"
        return response

    except Exception as e:
        print(f"Error occurred while generating sitemap: {e}")
        traceback.print_exc()
        return "An error occurred while generating the sitemap", 500

# Error handler for 404 errors
@app.errorhandler(404)
def page_not_found(e):
    return redirect(url_for('index'))

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/internet/')
def internet():
    return render_template('internet.html')


@app.route('/business/')
def business():
    return render_template('business.html')

@app.route('/bundles/')
def plans():
    return render_template('bundles.html')

@app.route('/blog/')
def blog():
    return render_template('blog.html', include_indexTag=False)
@app.route('/blog-post/')
def blogPost():
    return render_template('blog-post.html', include_indexTag=False)

    
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5004)
