body {
    margin: 0;
    font-family: 'Poppins', sans-serif;
}

/* footer */

.all-footer {
    background: linear-gradient(0deg, rgba(0, 145, 196, 0.95) 0%, rgba(0, 145, 196, 0.95) 100%), url(<path-to-image>), lightgray 0px -347.86px / 100.473% 287.526% no-repeat;
    height: 371px;
}

.all-footer-things {
    max-width: 1564px;
    margin: auto;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.footer-logos-images {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 22px;
}

.footer-list ul,
.site-map-list ul {
    display: flex;
    padding: 0;
    column-gap: 51px;
}

.footer-list ul li,
.site-map-list ul li {
    list-style: none;
}

.footer-list ul li a,
.site-map-list ul li a {
    text-decoration: none;
    color: #FFF;
    font-size: 14px;
    font-weight: 600;
    line-height: 25px;
}

.site-map-list ul li {
    position: relative;
}

.site-map-list ul li::after {
    position: absolute;
    top: 0;
    bottom: 0;
    right: -20px;
    content: "";
    height: 16px;
    background-color: #fff;
    width: 1px;
    margin: auto;
}

.footer-buttons,
.sitemap-buttons {
    display: flex;
    justify-content: space-between;
}

.social-icons {
    display: flex;
    column-gap: 20px;
}

.social-icons img {
    width: 30px;
    height: 30px;
}

.footer-lines {
    width: 100%;
    height: 1px;
    background-color: #fff;
}

.copy-right p {
    color: #FFF;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 25px;
}
.view-our-policy{
    padding-top: 33px;
}
.view-our-policy a {
    color: #FFF;
    font-size: 14px;
    font-weight: 400;
    line-height: 25px;
    text-decoration: none;
}