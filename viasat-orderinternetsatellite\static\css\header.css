body {
  margin: 0;
  font-family: "Open Sans", sans-serif;
}

.logo {
  margin-top: 7px;
}

.navbar {
  display: flex;
  align-items: center;
  padding: 25px 10px 10px;
  max-width: 1564px;
  margin: 0 auto;
  justify-content: space-between;
}

#menu {
  display: flex;
  gap: 60px;
  align-items: center;
}
.menu a {
  text-decoration: none;
  color: #4b4b4b;
  font-size: 16px;
  font-weight: 600;
}

.hire-developer-btn .hire-dev {
  color: #fff;
  font-size: 20px;
  width: 188px;
  height: 54px;
  font-weight: 700;
  background: #ff5820;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  column-gap: 8px;
}

.burger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.line {
  width: 25px;
  height: 3px;
  margin: 3px 0;
  background-color: #000;
}

@media (max-width: 768px) {
  .menu {
    display: none;
    flex-direction: column;
    position: absolute;
    top: 70px;
    left: 0;
    width: 100%;
    text-align: center;
    padding: 10px;
  }

  #menu {
    padding-left: 0px;
  }

  .menu.active {
    display: flex;
  }

  .burger {
    display: flex;
  }

  .navbar {
    justify-content: space-between;
  }
}
