.all-main img{
    max-width: 100%;
    width: unset;
}
.main_fonts p{
    font-family: var(--font-family-light);
}
.main_fonts h1,
.main_fonts h2,
.main_fonts h3,
.main_fonts h4,
.main_fonts h5,
.main_fonts h6{
    font-family: var(--font-family-primary);
}
.blog-banner-section .banner-background .banner-headings {
    max-width: 55rem;
}
.blog-banner-section .banner-background{
    background-image: url(/staticfiles/viasat-blog-banner.webp);
    height: 29.4375rem;
}
.blog_container{
    max-width: 105.625rem;
    padding: 0 1.875rem;
    margin: 0 auto;
}
/*blog ppost setion is start from here*/
.blog-post-section{
    padding: 3.75rem 0px;
}
.blog-form-grp {
    width: 18.75rem;
    position: relative;
    padding: 0;
    display: flex;
    align-items: stretch;
    flex-direction: column;
}
.blog-post-search{
    display: flex;
    align-items: center;
    justify-content: end;
    margin-bottom: 40px;
}
.blog-form-grp input{
    border: 1px solid #202E39;
    background-color: transparent;
    padding: .3125rem .9375rem;
    height: 1.125rem;
}
.blog-form-grp::placeholder{
    font-size: .875rem;
    line-height: 1;
    font-weight: 400;
    color: #000000;
}
.blog-form-grp button{
    border: none;
    background-color: transparent;
    position: absolute;
    top: .4375rem;
    right: .625rem;
    cursor: pointer;
}
.post-image-box {
    position: relative;
}
.single-post-details {
    position: absolute;
    bottom: 25px;
    left: 26px;
    right: 0;
}
.single-post-details p{
    font-size: .875rem;
    color: #fff;
    font-weight: 400;
    line-height: 1;
    background-color: #677A89;
    padding: 5px;
    display: inline-block;
    margin-bottom: 7px;
} 
.post-by {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: 10px;
}
.post-by h6{
    color: #fff;
    font-size: .875rem;
    font-weight: 500;
}
.post-data-box .name,
.post-data-box .pst-heading,
.post-data-box .pst-pera,
.post-data-box .date
{
    font-size: .875rem;
    color: #000;
    margin: 15px 0;
}
.single_post_flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4.375rem;
}
.post-data-box ,
.post-content .post-heading{
    border-bottom: 1px solid #202E39;
}
.post-content .post-heading{
    padding-bottom: 15px;
}

/*cta banner css is start from here*/
.blog-cta-section{
    background-image: url(/staticfiles/blog-cta-section-background.webp);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    padding: 3.75rem 0;
}
.cta-content h2 {
    font-size: 3.125rem;
    line-height: 1.2;
    font-weight: 300;
    color: #fff;
    max-width: 68.375rem;
    margin-bottom: 3.75rem;
}
.cta-content h2 span{
    font-weight: 600;
    color: #18ECFC;
}
.blog-cta-buttton{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 17.1875rem;
    height: 3.125rem;
    border:1px solid #fff;
    font-size: 1rem;
    font-weight: 600;
    color: #fff;
    gap: 10px;
    background-color: #0000006e;
    font-family: var(--font-family-semibold);
}

.blog-cta-buttton img{
    width: 2.3125rem;
    height: 2.3125rem;

}
.categories-button{
    margin-left: 5.625rem;
}
.categories-button select{
    border: none;
    background-color: transparent;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.2;
    cursor: pointer;
}
.categories-button select:focus-visible{
    border: none;
    outline: none;
}

/*All blogs css is start from here*/
.all-blog_post-section{
    padding: 3.75rem 0px;
}
.All_post_wraper {
    display: grid;
    grid-template-columns: auto auto auto auto;
    column-gap: 2.5rem;
    row-gap: 2.5rem;
}
.All_post_wraper .iner_post:nth-child(1){
    grid-column-start: 1;
    grid-column-end: 3;
    grid-row-start: 1;
    grid-row-end: 3;
}
.siner_post-content{
    display: flex;
    align-items: start;
    justify-content: start;
    gap: .75rem;
    padding: .625rem 0rem;
}
.siner_post-content .post-number{
    font-size: 3.125rem;
    line-height: 2.5rem;
    color: #000;
    font-family: var(--font-family-semibold);
}
.siner_post-content .post-text{
    font-size: 1.10rem;
    line-height: 1.2;
    color: #000;
    text-align: left;
    max-width: 27.4375rem;
}
.go-first-page-btn{
    border: none;
    background-color: transparent;
    display: flex;
    gap: 1.25rem;
    align-items: center;
    font-size:.875rem;
    font-family: bold;
    cursor: pointer;
}
.go-first-page-btn img{
    width: 1.5rem;
}
.next-prev-btns{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.25rem;
}
.next-prev-btns button{
    background-color: #202E39;
    border: 1px solid #202E39;
    color: #fff;
    width: 8.75rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: capitalize;
    cursor: pointer;
    transition: 0.2s;
}
.next-prev-btns button:hover{
    background-color: #fff;
    color: #202E39;
}
.page-count{
    font-size: .875rem;
    font-weight: 500;

}
.paginations {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 2.5rem;
}
/* blog-post-css is start from here */
.blog-post-banner {
    padding: 3.75rem 0 5rem 0;
    background: linear-gradient(90deg, rgba(1,103,129,1) 41%, rgba(26,57,71,1) 77%);
}
    .blog-content h1{
        font-size: 3.125rem;
        font-weight: 500;
        line-height: 1.3;
        font-family: var(--font-family-tertiary);
        color: #fff;
    }

    .blog-content .post-publish-date{
    font-size: .875rem;
    line-height: 1;
    color: #fff;
    margin-top: 2.5rem;
    font-weight: 400;
}
.main-blog-post-content h2{
    font-size: 1.375rem;
    line-height: 1.3;
    margin: 1.5625rem 0 0.6375rem 0;
    font-weight: 400;

}
.main-blog-post-content h3{
    margin: 1.5625rem 0 0.6375rem 0;
    line-height: 1.3;
    font-weight: 600;
}
.main-blog-post-content p {
    font-size: .875rem;
    line-height: 1.5;
    margin-bottom: 15px;
    font-weight: 400;
    color: #000;
}
.main-blog-post-content ol li,
.main-blog-post-content ul li{
    font-size: .875rem;
    line-height: 1.5;
    font-weight: 400;
}
.main-blog-post-content ol,
.main-blog-post-content ul{
    padding: 0;
    list-style-position: inside;
}
.main-blog-post{
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    width: 70%;
}
.main-blog-post > img {
    width: 100%;
}
.main-blog-post-content {
    padding: 1.25rem 2.5rem 2.5rem 2.5rem;
}
.related-persone{
    padding: 2.5rem;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    background-color: #fff;
}
.relt-flex{
    display: flex;
    align-items: center;
    justify-content: start;
    margin-bottom: 2.5rem;
}
.related-side {
    width: 27%;
}
.share_social {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: .9375rem;
    margin-top: 2.5rem;
}
.share_social p{
    font-size: 1.125rem;
    font-weight: 400;
    line-height: 1.3;
}
.share_social ul {
    display: flex;
    list-style: none;
    gap: .625rem;
    padding: 0;
    margin: 0; 
}
.btm_sec-wrap {
    display: flex;
    align-items: start;
    justify-content: space-between;
    margin-top: -2.5rem;
}
.related-pr-content h4{
    font-size: .875rem;
    line-height: 1.3;
    color: #000;
    margin-bottom: 14px;
}

.related-pr-content h6,
.relt-pera{
    font-size: .75rem;
    line-height: 1.4;
    font-weight: 400;
}
.relt-flex > img {
    border-radius: 100%;
    margin-right: .9375rem;
}
.recent-posts h3{
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.3;
    color: #202E39;
    padding-bottom: .9375rem;
    border-bottom: 1px solid #707070;
}
.recent-posts {
    margin-top: 2.5rem;
}
.recent-name-btm{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.recent-post-box{
    border-bottom: 1px solid #707070;
    padding:0 0 .9375rem 0;
    margin: 20px 0;
}
.recent-post-box > img{
    width: 100%;
    height: auto;
}
.recent-post-box-content p{
    font-size: .875rem;
    line-height: 1.3;
    font-weight: 400;
    margin: .9375rem 0;
}
.recent-name-btm h5,.recent-name-btm h6{
    font-size: .75rem;
    line-height: 1.3;
    font-weight: 400;
}
.recent-name-btm h5 span{
    font-weight: 700;
}
.short-cta-button{
    background: linear-gradient(0deg, rgba(1,103,129,1) 33%, rgba(26,57,71,1) 100%);
    padding: 2.5rem;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}
.short-cta-button h3 {
    color: #fff;
    font-size: 1.875rem;
    margin-bottom: 15px;
    line-height: 1.3;
    font-weight: 700;
}
.short-cta-button p {
    color: #fff;
    font-size: 1rem;
    line-height: 1.4;
    margin-bottom: 25px;
}
.blog-ppost-main-section{
    padding:0 0 3.75rem 0;
}
    .related-post-search{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .related-post-search h2{
        font-size: 1.125rem;
        line-height: 1.3;
        color: #000;
        font-weight: 700;
    }
    .related-pst-wraper {
        display: flex;
        align-items: stretch;
        justify-content: space-between;
    }
    .read-more-post {
        font-size: 1.125rem;
        line-height: 1.3;
        font-weight: 500;
        color: #fff;
        padding: .8125rem 1.875rem;
        background-color: #202E39;
        display: inline-block;
        font-family: var(--font-family-semibold);
    }
    .recent-post-box-content h4{
        font-size: 1.125rem;
        font-weight: 400;
        line-height: 1.3;
        margin-top: 1.25rem;
    }
    .related-pst-wraper .recent-post-box {
        border: 1px solid #707070;
        padding:0;
        width: 24%;
    }
    .related-pst-wraper .recent-post-box .recent-post-box-content{
        padding: 1.25rem;
    }

/*respponsive css is start from here*/
@media screen and (max-width:1500px) {
    .blog-form-grp button {
        top: 0.2375rem;
    }
}
@media screen and (max-width:1024px){
    .main-blog-post {
        width: 65%;
    }
    .related-side {
        width: 32%;
    }
    .short-cta-button {
        padding: 1.5rem;
    }
    .short-cta-button h3 {
        font-size: 1.675rem;
        line-height: 1.2;
    }
    .related-pst-wraper{
        flex-wrap: wrap;
    }
    .related-pst-wraper .recent-post-box {
        width: 32%;
    }
}
@media screen and (max-width:768px) {
    .All_post_wraper {
        display: grid;
        grid-template-columns: auto auto;
        column-gap: 2.5rem;
        row-gap: 2.5rem;
    }
    .single_post_flex {
        flex-direction: column;
    }
    .blog-banner-section .banner-background {
        height: 18.4375rem;
    }
    .categories-button {
        margin-left: 0.625rem;
    }
    .cta-content h2 {
        font-size: 2.125rem;
        margin-bottom: 2.75rem;
    }
    .btm_sec-wrap {
        flex-direction: column;
    }
    .main-blog-post,
    .related-side{
        width: 100%;
        margin: .9375rem 0;
    }
    .blog-content h1 {
        font-size: 2.125rem;
    }
    .related-pst-wraper .recent-post-box {
        width: 48%;
    }
    .blog-content h1 {
        font-size: 1.625rem;
    }
}
@media screen and (max-width:568px) {
    .All_post_wraper {
        grid-template-columns: auto;
    }
    .All_post_wraper .iner_post:nth-child(1) {
        grid-column-start: unset;
        grid-column-end: unset;
        grid-row-start: unset;
        grid-row-end: unset;
    }
    .related-pst-wraper .recent-post-box {
        width: 100%;
    }
}