.business-banner-section .hughsenet-heading span {
  color: #0091c4;
}

.business-banner-section .banner-background {
  height: 840px;
  background: url(/staticfiles/hughesnet-business-page-banner-image.webp);
}

/* business form */
.business-banner-section .hughsenet-section {
  justify-content: space-between;
}

.business-banner-section .hughsenet-heading1 {
  max-width: 29.4%;
}

.top-bar {
  background-color: #0091c4;
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  line-height: 45px;
  padding: 15px 0px;
  display: flex;
  align-items: center;
  column-gap: 16px;
  justify-content: center;
}

.top-bar img {
  width: unset;
}

.form-container {
  padding: 20px;
  background-color: #fff;
}

.form-field {
  margin-bottom: 10px;
}

.form-field input {
  width: 96%;
  padding: 13px 0px;
  border-radius: 3px;
  border: 0.5px solid #8d8d8d;
  background: #f9f9f9;
  color: #000;
  font-size: 14px;
  font-weight: 300;
  line-height: 18px;
  padding-left: 16px;
}

.submit-button {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  line-height: 18px;
  width: 100%;
  padding: 13px 0px;
  background-color: #0091c4;
  border: none;
}

.form-container p {
  color: #000;
  text-align: justify;
  font-size: 12px;
  font-weight: 300;
  line-height: 18px;
  margin-top: 37px;
}

.business-package-background {
  background-image: url(/staticfiles/hughesnet-business-page-package-card.webp);
  height: 640px;
  padding-top: 98px;
  background-repeat: no-repeat;
}

.package-cards-items {
  display: flex;
  max-width: 1024px;
  margin: 0 auto;
  justify-content: space-between;
}

.package-cards-item1 {
  background: #fff;
  box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.1);
  padding: 21px;
  width: 26%;
}

.internet-card-heading {
  display: flex;
  align-items: center;
  column-gap: 20px;
  margin-bottom: 22px;
}

.internet-card-heading span {
  color: #0091c4;
  font-size: 30px;
  font-style: normal;
  font-weight: 700;
  line-height: 30px;
}

.package-cards-item1 p,
.package-cards-item1 ul li {
  color: #000;
  text-align: justify;
  font-size: 15px;
  font-style: normal;
  font-weight: 300;
  line-height: 20px;
}

.cards-paragraph p {
  color: #fff;
  text-align: justify;
  font-size: 14px;
  line-height: 25px;
  margin: 0 auto;
  margin-top: 70px;
  max-width: 1563px;
  padding: 0 20px;
}
.hughsnet-internet-plans .all-online-fast .online-fast-heading h2 {
  font-size: 32px;
}
.internet-avilable-my-area {
  background-color: transparent;
}
.internet-avilable-my-area .at-all-glace {
  background: url(/staticfiles/hughsnet-business-page-internet-services-my-area-image.webp);
}
.my-area-services p {
  max-width: 670px;
}
.internet-avilable-my-area .glace-1 .glace-heading {
  width: 41%;
}

@media only screen and (max-width: 1350px) {
  .business-banner-section .hughsenet-section .hughsenet-heading {
    width: 48%;
  }
  .business-banner-section .hughsenet-heading1 {
    max-width: 41.4%;
  }
  .package-cards-items {
    max-width: 850px;
  }
  .package-cards-item1 p,
  .package-cards-item1 ul li {
    font-size: 13px;
  }
  .internet-card-heading span {
    font-size: 22px;
  }
  .cards-paragraph p {
    font-size: 13px;
    margin-top: 27px;
  }
  .hughsnet-internet-plans .all-online-fast .online-fast-heading h2 {
    font-size: 25px;
  }
  .internet-avilable-my-area .glace-1 .glace-heading {
    width: 51%;
  }
  .my-area-services p {
    max-width: 451px;
  }
  .lot-more-value.internet-avilable-my-area .at-all-glace {
    height: unset;
    background-size: cover;
    background-repeat: no-repeat;
    padding: 25px 0;
    background-position: top;
  }
}
@media only screen and (max-width: 941px) {
  .business-banner-section .hughsenet-section {
    flex-direction: row;
  }
  .business-banner-section .hughsenet-heading h1 {
    font-size: 30px;
    line-height: 40px;
  }
  .all-main p {
    font-size: 14px;
  }
  .top-bar {
    font-size: 16px;
    padding: 6px 0px;
  }
  .form-field input {
    width: 93%;
  }
  .business-package-background {
    padding-top: 60px;
  }
  .package-cards-items {
    max-width: 714px;
  }
  .internet-card-heading span img {
    max-width: 70%;
    transform: translate(0px, 4px);
  }
  .internet-card-heading {
    column-gap: 0px;
    margin-bottom: 8px;
  }
  .internet-card-heading p {
    font-size: 13px;
    line-height: normal;
  }
  .cards-paragraph p {
    font-size: 13px;
    line-height: normal;
  }
  .business-package-background {
    height: 678px;
  }
  .hughsnet-internet-plans .all-online-fast .online-fast-heading h2 {
    text-align: center;
  }
  .internet-avilable-my-area .glace-1 .glace-heading {
    width: 100%;
  }
  .internet-avilable-my-area .glace-heading h2 {
    max-width: 400px;
  }
  .internet-avilable-my-area {
    padding: 0 0;
  }
}

@media only screen and (max-width: 767px) {
  .business-banner-section .banner-background {
    background: url(/staticfiles/hughesnet-business-page-banner-image-mobile.webp);
    height: 1280px;
    padding-top: 34px;
  }
  .business-banner-section .hughsenet-section {
    flex-direction: column;
    height: 96%;
  }
  .business-banner-section .hughsenet-section .hughsenet-heading,
  .business-banner-section .hughsenet-heading1 {
    width: 100%;
    max-width: 100%;
  }
  .package-cards-items {
    flex-direction: column;
    row-gap: 20px;
  }
  .package-cards-item1 {
    width: 67%;
    margin: 0 auto;
  }
  .business-package-background {
    height: 1711px;
    background: url(/staticfiles/hughesnet-business-page-package-card-mobile.webp);
  }
  .internet-card-heading span {
    font-size: 30px;
  }
  .internet-card-heading span img {
    max-width: 100%;
    transform: unset;
  }
  .internet-card-heading {
    column-gap: 20px;
  }
  .cards-paragraph p {
    margin-top: 134px;
  }
  .business-package-background {
    padding-top: 70px;
  }
  .online-fast-section.hughsnet-internet-plans {
    display: none;
  }
  .at-a-glance.lot-more-value.internet-avilable-my-area {
    margin-top: 0;
  }
  .at-a-glance.lot-more-value.internet-avilable-my-area .at-all-glace {
    background: url(/staticfiles/hughsnet-business-page-internet-services-my-area-image.-mobile-responsive.webp);
    height: 887px;
  }
  .at-a-glance.lot-more-value.internet-avilable-my-area .at-all-glace .glace-1 {
    justify-content: start;
  }
  .main-button.giving-your-more-freedom {
    justify-content: center;
    margin-top: 10px;
  }
}
