/* Navbar */
/* .el-ar-nav {
    padding: 1.5rem;
}
.el-agents {
    display: flex;
    align-items: center;
    max-width: 80rem;
    margin: 0 auto;
    gap: 3rem;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
}
.el-agents img {
    width: 202px;
    height: 60px;
    margin: 0.8rem 0;
}

.el-agents .el-internet {
    width: 20%;
    background: #0077a4;
    text-align: center;
}

.el-ar-nav .el-internet img {
        width: 100%;
        height: 5rem;
        margin: 0.8rem 0;
        object-fit: contain;
}

.el-agents .el-services {
    width: 55%;
    display: flex;
    align-items: center;

}

.el-agents .el-services .el-home,
.el-agents .el-services .el-account,
.el-agents .el-services .el-account a {
    display: flex;
    align-items: center;
    column-gap: 3rem;
}
.el-agents .el-services .el-account a {
    column-gap: 0.8rem;
}
.el-agents .el-ar-live {
    width: 13%;
    background: #ff8600;
    display: flex;
    align-items: center;
    column-gap: 0.5rem;
    justify-content: center;
    transform: translate(38px, 0px);
}
.el-agents .el-ar-live img {
    width: 20px;
    height: 20px;
}

.el-agents .el-services .el-home a,
.el-agents .el-services .el-account p {
    color: #000;
    font-size: 1rem;
    font-weight: 500;
}

.el-agents .el-services .el-home a:hover,
.el-agents .el-services .el-account p:hover,
.el-agents .el-avail-agents a:hover {
    color: #0077a4;
}

.el-agents .el-services .el-account img {
    width: 20px;
    height: 20px;
}

.el-agents .el-avail-agents img {
    width: 60px;
    border-radius: 50%;
    height: 60px;
    object-fit: cover;
    transform: translate(-25px, 2px);
}

.el-ar-live {
    text-align: left;
    transform: translate(-18px, 0px);
}

.el-ar-live a {
    color: #fff;
}

@media screen and (max-width:1200px) {
    html {
        font-size: 14px;
    }
} */
.flex-navbar {
  display: flex;
  padding: 1rem 0px;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
}
.flex-wrap {
  flex-wrap: wrap;
}
.all-header {
  background-color: #fff;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
  padding: 1rem 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
}
@keyframes stickyEffect {
  from {
    background: #333;
  }
  to {
    background: #555;
  }
}
nav {
  max-width: 80rem;
  min-height: 8vh;
  margin: 0 auto;
}
.logo {
  display: flex;
}
.nav-links {
  display: flex;
  width: 40%;
  column-gap: 2rem;
  z-index: 999;
}
.nav-links li {
  list-style: none;
}
.nav-links a {
  text-decoration: none;
  letter-spacing: 0.1875rem;
  font-size: 0.875rem;
  font-weight: 550;
  color: #000;
}
.burger-button {
  display: none;
}
.burger-button div {
  width: 2.0375rem;
  height: 0.1875rem;
  background-color: #ff8600;
  margin: 0.35rem;
  border-radius: 0.3125rem;
  transition: all 0.3s ease;
}
.unlimeted-logo {
  width: 30%;
}
.unlimeted-logo img {
  width: 100%;
}
.el-ar-live {
  width: 14%;
}
.el-ar-live a {
  background: #ff8600;
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 0.5rem;
  padding: 0.8rem 0;
  color: #fff;
}
.el-ar-live img {
  width: 100%;
  max-width: 10%;
}
/* .el-ar-live a {
  color: #fff;
  display: flex;
  align-items: center;
  column-gap: 0.5rem;
} */
@media screen and (max-width: 900px) {
  body {
    overflow-x: hidden;
  }
  .burger-button {
    display: block;
    cursor: pointer;
    width: 7%;
  }
  .nav-links {
    position: absolute;
    right: 0rem;
    top: 11vh;
    height: unset;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 50%;
    transform: translateX(100%);
    transition: transform 0.5s ease-in;
    box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
    display: none;
  }
  .all-header {
    padding: 0;
  }
  .nav-links.nav-active {
    display: block;
    padding: 2.5rem 1rem;
  }
  .nav-links li {
    opacity: 0;
    text-align: center;
  }
  .el-ar-live {
    width: 26%;
  }
  .unlimeted-logo {
    width: 47%;
  }
  .flex-navbar {
    padding: 1rem;
  }
  .nav-links a {
    font-size: 1.3rem;
    line-height: 3.5rem;
  }
  .unlimeted-logo {
    width: 76%;
  }
  .el-ar-live {
    display: none;
  }
}
@media screen and (max-width: 1200px) {
  .nav-links {
    width: 50%;
  }
}

@keyframes navLinksFade {
  from {
    opacity: 0;
    transform: translateX(3.125rem);
  }
  to {
    opacity: 1;
    transform: translateX(0rem);
  }
}

.nav-active {
  transform: translateX(0rem);
}
.toggle-burger .line1 {
  transform: rotate(-45deg) translate(-0.1875rem, 0.4375rem);
}
.toggle-burger .line2 {
  opacity: 0;
}
.toggle-burger .line3 {
  transform: rotate(45deg) translate(-0.125rem, -0.5rem);
}
