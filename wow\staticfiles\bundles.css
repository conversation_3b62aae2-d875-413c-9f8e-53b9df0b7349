:root {
  --color-primary: #c62328;
  --color-secondary: #58595b;
  --color-tertiary: #f26529;
  --color-tertiary: #f26529;
  --color-red: #c62328;
  --color-blue: #0072ab;
  --color-yellow: #ffc40c;
}
.bundels-top-bar .top-bar-bundles {
  background-color: var(--color-secondary);
}
.bundels-top-bar .top-bar-bundles p {
  color: var(--color-white);
}
.bundles-banner-section .banner-background {
  background-image: url(/staticfiles/wow-bundles-page-banner-image.webp);
  background-size: cover;
}

.bundles-banner-section .Wow-heading-list h2 {
  color: var(--color-blue);
}

.Wow-heading-list ul {
  padding-left: 15px;
}
.top-bar-bundles p {
  font-size: 20px;
}
.Wow-heading-list ul li p {
  font-size: 14px;
  line-height: 20px;
  max-width: 500px;
  margin: 0;
}
.Wow-heading-list p:nth-child(3) {
  max-width: 498px;
  font-size: 12px;
}
.Internet-tv-button a {
  display: flex;
  align-items: center;
  width: 176px;
  height: 56px;
  padding: 0;
  justify-content: center;
  column-gap: 15px;
}

.Internet-tv-button a img {
  width: 16.5px;
  height: 7px;
  transform: translate(0px, 3px);
}

.bundles-page .about-services-heading h2,
.bundles-page .about-services-heading h2 span {
  color: var(--color-white);
}

.bundles-page .call-now-button a {
  background-color: var(--color-blue);
}

.bundles-page .question-banner {
  top: 102px;
}

.bundles-services .our-services-background {
  background-image: url(/staticfiles/wow-phonepage-our-services-background-image.webp);
  padding-top: 36px;
}

.bundles-services .one::before {
  background-color: var(--color-red);
}

.bundles-services .three::before {
  background-color: var(--color-blue);
}

.bundles-services .four::before {
  background-color: var(--color-yellow);
}

.bundles-services .our-services-heading p {
  color: #000;
  font-family: var(--font-ars-maquette-light);
  text-align: justify;
  font-size: 18px;
  font-weight: 300;
  line-height: 24px;
  margin-top: 26px;
  max-width: 1000px;
}

.bundles-services .our-services-heading {
  margin-bottom: 23px;
}

.bundles-services .our-services-card-sec .our-services-card1:nth-child(2) h3 {
  color: var(--color-red);
}

.bundles-services .our-services-card-sec .our-services-card1:nth-child(4) h3 {
  color: var(--color-blue);
}

.bundles-services .our-services-card-sec .our-services-card1:nth-child(5) h3 {
  color: var(--color-yellow);
}

.netflix-card-section .gaming-card-1 h3 {
  color: var(--color-quinary);
}

.netflix-card-section .Internet-tv-button a {
  background-color: var(--color-quinary);
  width: 193.14px;
}

/* table css */
.pricing-table {
  border-collapse: collapse;
  width: 100%;
}

.row {
  border-bottom: 2px solid #333;
}

.col1 {
  background-color: #f1f1f1;
}

.col2 {
  background-color: #e1f5ff;
}

.col3 {
  background-color: #e4ffd3;
}

.col4 {
  background-color: #ffe5da;
}

.col5 {
  background-color: #ffd2d4;
}

.compare-wow {
  max-width: 1454px;
  margin: 0 auto;
}

.pricing-table tr td {
  text-align: center;
  color: #58595b;
  font-size: 18px;
  font-family: var(--font-ars-maquette-light);
  padding: 34px 27px;
}
.pricing-table tr:nth-child(1) td {
  font-family: var(--font-ars-maquette-medium);
}
.pricing-table tr:nth-child(1) td:nth-child(3) {
  color: #0072ab;
}
.pricing-table tr:nth-child(1) td:nth-child(4) {
  color: #408a13;
}
.pricing-table tr:nth-child(1) td:nth-child(5) {
  color: #f26529;
}
.pricing-table tr:nth-child(1) td:nth-child(6) {
  color: #c62328;
}
.pricing-table tr:nth-child(5) td:nth-child(3) {
  color: #0072ab;
}
.pricing-table tr:nth-child(5) td:nth-child(4) {
  color: #408a13;
}
.pricing-table tr:nth-child(5) td:nth-child(5) {
  color: #f26529;
}
.pricing-table tr:nth-child(5) td:nth-child(6) {
  color: #c62328;
}
.pricing-table tr td.feature,
.pricing-table tr th.header {
  text-align: right;
  color: var(--color-black);
  font-family: var(--font-ars-maquette-medium);
  font-size: 18px;
  font-weight: 500;
  padding: 28px;
}
.table-heading h2 {
  text-align: center;
  font-family: var(--font-ars-maquette-light);
  font-size: 40px;
  font-weight: 300;
  margin-bottom: 90px;
}
.pricing-table-section {
  padding: 80px 0px 121px 0px;
  background: #f9f9f9;
  overflow-y: auto;
}
.services-availability-heading h3 {
  color: var(--color-primary);
}
.visa-text ul {
  padding-left: 43px;
}
.services-availability {
  padding: 64px 0px;
}
@media screen and (max-width: 745px) {
  .bundles-page .question-banner {
    top: 10px;
  }
  /* .bundles-page .wow-banner-images {
        transform: translate(10px, 72px);
    } */
  .table-heading h2 {
    font-size: 30px;
    margin-bottom: 45px;
  }
  .pricing-table-section {
    padding: 50px 0px 42px 0px;
  }
}
