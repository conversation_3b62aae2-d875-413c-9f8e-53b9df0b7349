<?xml version="1.0" encoding="UTF-8"?>
<urlset
      xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
            http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">


<url>
    <loc>{{ host_base }}/</loc>
    <lastmod>{{ file_info['index'] }}</lastmod>
    <priority>1.00</priority>
</url>

<!-- Other URLs -->
{% for url in static_urls %}
  {% if url['loc'] != host_base %}
    <url>
      <loc>{{ url['loc'] }}/</loc>
      {% if ('/' in url['loc']) %}
      <lastmod>{{ file_info.get(url['loc'].split('/')[-1], 'default_date_if_missing') }}</lastmod>
      {% endif %}
      <priority>0.80</priority>
    </url>
  {% endif %}
{% endfor %}

</urlset>
