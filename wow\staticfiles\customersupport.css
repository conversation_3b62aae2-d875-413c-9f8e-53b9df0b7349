:root {
  --color-primary: #f26529;
  --color-secondary: #408a13;
  --color-tertiary: #f26529;
  --color-tertiary: #f26529;
  --color-red: #c62328;
  --color-blue: #0072ab;
  --color-yellow: #ffc40c;
  --color-grey: #58595b;
}
.customer-top-bar .top-bar-bundles {
  background-color: var(--color-secondary);
}
.customer-top-bar .top-bar-bundles p {
  color: var(--color-white);
}
.customer-section-banner .banner-background {
  background-image: url(/staticfiles/wow-customersupport-banner-image.webp);
  background-size: cover;
}

.customer-section-banner .Wow-heading h2 {
  font-size: 25px;
  font-family: var(--font-ars-maquette-regular);
  line-height: 35px;
  font-weight: 400;
  line-height: 30px;
}

.customer-section-banner .Wow-heading-list p {
  max-width: 545px;
  font-size: 18px;
  font-family: var(--font-ars-maquette-light);
  text-align: justify;
  line-height: 30px;
}

.Internet-tv-button a {
  display: flex;
  align-items: center;
  width: 176px;
  height: 56px;
  padding: 0;
  justify-content: center;
  column-gap: 15px;
}

.Internet-tv-button a img {
  width: unset;
  transform: translate(0px, 1px);
}

.customer-page .about-services-heading h2,
.customer-page .about-services-heading h2 span {
  color: var(--color-white);
}

.customer-page .call-now-button a {
  background-color: var(--color-yellow);
}

.customer-page .question-banner {
  top: 102px;
}

.customer-services .our-services-background {
  background-image: url(/staticfiles/wow-customerservices-our-services-background-image.webp);
  padding-top: 36px;
}

.customer-services .one::before {
  background-color: var(--color-red);
}

.customer-services .three::before {
  background-color: var(--color-blue);
}

.customer-services .four::before {
  background-color: var(--color-yellow);
}
.customer-services .our-services-card1 {
  width: 32%;
}
.customer-services .our-services-heading p {
  color: #000;
  font-family: var(--font-ars-maquette-light);
  text-align: justify;
  font-size: 18px;
  font-weight: 300;
  line-height: 24px;
  margin-top: 26px;
  max-width: 1000px;
}

.customer-services .our-services-heading {
  margin-bottom: 23px;
}

.customer-services .our-services-card-sec .our-services-card1:nth-child(2) h3 {
  color: var(--color-red);
}

.customer-services .our-services-card-sec .our-services-card1:nth-child(4) h3 {
  color: var(--color-blue);
}

.customer-services .our-services-card-sec .our-services-card1:nth-child(5) h3 {
  color: var(--color-yellow);
}

.all-services-boxex {
  max-width: 1626px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
}

.customer-card-1 {
  width: 19.4%;
  background-color: var(--color-white);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);
  padding: 35px;
  position: relative;
}

.customer-card-1::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 12px;
  background-color: var(--color-primary);
}

.customer-card-1 span {
  color: var(--color-primary);
  font-family: var(--font-ars-maquette-medium);
  font-size: 16px;
  font-weight: 500;
}

.customer-card-1 h3 {
  color: var(--color-primary);
  font-family: var(--font-ars-maquette-medium);
  font-size: 24px;
  font-weight: 500;
}

.customer-card-1 ul {
  padding-left: 25px;
  border-bottom: 0.5px solid #58595b;
  padding-bottom: 35px;
  margin-bottom: 26px;
}

.customer-card-1 ul li {
  font-family: var(--font-ars-maquette-light);
  font-size: 18px;
  font-weight: 300;
}

.take-now a {
  background-color: var(--color-primary);
  width: 166px;
  height: 49px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: 18px;
  font-family: var(--font-ars-maquette-medium);
  font-weight: 500;
  margin: 0 auto;
}

.customer-services-heading h2 {
  font-family: var(--font-ars-maquette-light);
  font-size: 40px;
  font-weight: 300;
  text-align: center;
  position: relative;
  z-index: 9;
}

.all-services-boxex {
  padding-top: 57px;
}

.customer-services-section {
  padding: 60px 0px;
  position: relative;
}

.customer-services-section::before {
  border-radius: 10px 0px 0px 0px;
  background-color: #f6f6f6;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 285px;
  content: "";
}

.coloring-change1 span,
.coloring-change1 h3 {
  color: var(--color-secondary);
}

.coloring-change1 .take-now a,
.coloring-change1::before {
  background-color: var(--color-secondary);
}

.coloring-change2 span,
.coloring-change2 h3 {
  color: var(--color-blue);
}

.coloring-change2 .take-now a,
.coloring-change2::before {
  background-color: var(--);
}

.coloring-change3 span,
.coloring-change3 h3 {
  color: var(--color-grey);
}

.coloring-change3 .take-now a,
.coloring-change3::before {
  background-color: var(--color-grey);
}

.gaming-card-1 h3 {
  color: var(--color-blue);
}

.Internet-tv-button a {
  background-color: var(--color-blue);
}

.netflix-card-section .gaming-card-1 h3 {
  color: var(--color-red);
}

.netflix-card-section .Internet-tv-button a {
  background-color: #c62328;
}

.enjoy-family ul {
  padding-left: 25px;
}

.enjoy-family ul li {
  font-family: var(--font-ars-maquette-light);
  font-size: 18px;
  font-weight: 300;
  line-height: 30px;
}
.accordion .accordion-item {
  background-color: var(--color-red);
}
.services-availability-heading h3 {
  color: var(--color-red);
}
@media screen and (max-width: 1700px) {
  .all-services-boxex {
    max-width: 1420px;
  }
}
@media screen and (max-width: 1520px) {
  .all-services-boxex {
    max-width: 1320px;
  }
  .customer-card-1 {
    width: 18.4%;
  }
  .customer-card-1 ul li {
    font-size: 16px;
  }
}
@media screen and (max-width: 1439px) {
  .all-services-boxex {
    max-width: 1190px;
  }
  .customer-card-1 h3 {
    font-size: 22px;
  }
  .customer-card-1 {
    width: 20%;
    padding: 35px 20px;
  }
}
@media screen and (max-width: 1250px) {
  .all-services-boxex {
    flex-wrap: wrap;
    max-width: 900px;
    row-gap: 30px;
  }
  .customer-card-1 {
    width: 40.6%;
    padding: 35px;
  }
}
@media screen and (max-width: 975px) {
  .all-services-boxex {
    max-width: 700px;
  }
  .customer-card-1 {
    width: 38%;
  }
}
@media screen and (max-width: 767px) {
  .all-services-boxex {
    flex-direction: column;
  }
  .customer-card-1 {
    width: 70%;
    margin: 0 auto;
  }
  .customer-services-heading h2 {
    font-size: 25px;
  }
  .customer-section-banner .Wow-heading-list p {
    font-size: 14px;
    line-height: normal;
  }
  .customer-section-banner .banner-background {
    height: unset;
  }
  .customer-page .question-banner {
    top: 0px;
  }
  .customer-services .our-services-card1 {
    width: 100%;
  }
}
