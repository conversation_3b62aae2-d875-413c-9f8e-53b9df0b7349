.all-main .sell-information {
  max-width: 75rem;
  margin: 0 auto;
  padding: 0 1rem;
}
.all-main section.lcd-features {
  text-align: center;
  padding: 80px 0 0px 0;
}
.all-main .lcd-features-new {
  margin-bottom: 80px;
}
.all-main .sub-heading-detail h1 {
  font-weight: 600;
  font-size: 40px;
  margin-bottom: 30px;
}
.all-main .sub-heading-detail h2 {
  font-weight: 600;
  font-size: 36px;
  margin-bottom: 30px;
}
.all-main .sub-heading-detail h3 {
  font-size: 26px;
  text-align: center;
  font-weight: 600;
}
.all-main .sub-heading-detail p {
  max-width: 60%;
  font-size: 18px;
  margin: 0 auto;
}

.all-main .sub-heading-detail {
  margin-bottom: 70px;
}
.all-main .provider-review-feedbackform {
  padding: 50px;
  background: #f8f8f8;
}
.all-main .form-group {
  margin-bottom: 1rem;
  text-align: left;
}

.all-main .form-text {
  display: block;
  margin-top: 0.25rem;
}

.all-main .form-row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}

.all-main .form-row > .col,
.all-main .form-row > [class*="col-"] {
  padding-right: 5px;
  padding-left: 5px;
}

.all-main .form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}

.all-main .form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}

.all-main .form-check-input:disabled ~ .form-check-label,
.all-main .form-check-input[disabled] ~ .form-check-label {
  color: #6c757d;
}

.form-check-label {
  margin-bottom: 0;
}

.form-check-inline {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}
.form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.form-group label {
  display: inline-block;
  margin-bottom: 0.5rem;
}
.sm-disclaimer small {
  font-size: 0.8rem;
}
.feedbackbtn {
  background: #0077a4;
  color: #fff;
  font-weight: 600;
  padding: 10px 30px;
  border-radius: 50px;
  white-space: nowrap;
  border: 0;
  cursor: pointer;
}
.opt-aout-butn {
  text-align: center;
}
.sm-disclaimer {
  margin-bottom: 3rem;
}
.do-not-sell-my-information p {
  text-align: center;
  margin: 0 auto;
}
