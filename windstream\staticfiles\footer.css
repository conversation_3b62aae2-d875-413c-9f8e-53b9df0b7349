/* footer */

.all-footer {
  background: #231f20;
  height: 371px;
}

.all-footer-things {
  max-width: 1564px;
  margin: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.footer-logos-images {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 22px;
}

.footer-list ul,
.site-map-list ul {
  display: flex;
  padding: 0;
  column-gap: 51px;
  flex-wrap: wrap;
  row-gap: 10px;
}

.footer-list ul li,
.site-map-list ul li {
  list-style: none;
}

.footer-list ul li a,
.site-map-list ul li a {
  text-decoration: none;
  color: #fff;
  font-size: 14px;
  line-height: 25px;
  font-weight: 400;
  letter-spacing: 1px;
}

.site-map-list ul li {
  position: relative;
}

.site-map-list ul li::after {
  position: absolute;
  top: 0;
  bottom: 0;
  right: -20px;
  content: "";
  height: 16px;
  background-color: #fff;
  width: 1px;
  margin: auto;
}

.footer-buttons,
.sitemap-buttons {
  display: flex;
  justify-content: space-between;
}

.social-icons {
  display: flex;
  column-gap: 20px;
}

.social-icons img {
  width: 30px;
  height: 30px;
}

.footer-lines {
  width: 100%;
  height: 1px;
  background-color: #fff;
}

.copy-right p {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 25px;
}
.view-our-policy {
  padding-top: 33px;
}
.view-our-policy a {
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  line-height: 25px;
  text-decoration: none;
}
.all-footer {
  position: relative;
}
.all-footer::before {
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  right: 0;
  background-image: url(/static/images/header-footer-line.webp);
  height: 10px;
}
.footer-logos-images img {
  width: unset;
  max-width: 17%;
}
.ctaa-button.call-now-button {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: none;
  z-index: 9999;
}
.ctaa-button.call-now-button img {
  width: 25px;
  height: 25px;
}
.ctaa-button.call-now-button a {
  background: linear-gradient(272deg, #22a881 0%, #007c83 100%);
  width: 100%;
  text-align: center;
  font-size: 2.3rem;
  font-weight: 700;
  display: flex;
  justify-content: center;
  column-gap: 0.7rem;
  padding: 1.3rem 0;
  align-items: center;
  color: #fff;
}
.ctaa-button.call-now-button a {
  text-decoration: none;
}
@media only screen and (min-width: 1451px) {
  .all-footer-things {
    max-width: 1280px;
  }
}
@media screen and (max-width: 1450px) {
  .all-footer-things {
    max-width: 1100px;
    padding: 0 20px;
  }
}
@media screen and (max-width: 745px) {
  .ctaa-button.call-now-button {
    display: block;
  }
  .footer-logos-images,
  .footer-buttons {
    flex-direction: column;
  }
  .footer-list ul li:nth-child(1) {
    display: none;
  }
  .footer-list ul {
    column-gap: 12px;
  }
  .footer-list ul li a,
  .site-map-list ul li a {
    font-size: 12px;
    font-weight: 300;
  }
  .site-map-list ul li:nth-child(2)::after {
    display: none;
  }
  .site-map-list ul {
    column-gap: 26px;
  }
  .site-map-list ul li::after {
    right: -13px;
  }
  .social-icons {
    justify-content: center;
    padding-bottom: 38px;
  }
  .footer-buttons {
    flex-direction: column-reverse;
  }

  .sitemap-buttons {
    flex-direction: row-reverse;
  }
  .footer-logos-images img:nth-child(1) {
    width: 119px;
  }
  .all-footer {
    height: unset;
    padding: 25px 0px;
    margin-bottom: 5rem;
  }
}
@media screen and (max-width: 389px) {
  .site-map-list ul {
    column-gap: 23px;
  }
  .copy-right p {
    font-size: 12px;
    line-height: normal;
  }
  .sitemap-buttons {
    align-items: center;
  }
  .all-footer-things {
    padding: 0 10px;
  }
}
