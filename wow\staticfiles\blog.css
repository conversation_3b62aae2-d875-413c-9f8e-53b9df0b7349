.blog-banner .banner-background {
  background-image: url(/staticfiles/blog-banner.webp);
  height: 43.75rem;
}
/* searchbutton */
.search-box {
  position: relative;
  display: flex;
}

.search-input {
  width: 100%;
  font-size: 1rem;
  padding: 0.9375rem 3.75rem 0.9375rem 1.5625rem;
  background-color: #1f2937;
  color: #ffffff;
  border-radius: 0.375rem;
  border: none;
  transition: all 0.4s;
  border-radius: 1.875rem;
  font-size: 1.25rem;
}
.search-input::placeholder {
  color: #fff;
}
.search-input:focus {
  border: none;
  outline: none;
  box-shadow: 0 0.0625rem 0.75rem #b8c6db;
  -moz-box-shadow: 0 0.0625rem 0.75rem #b8c6db;
  -webkit-box-shadow: 0 0.0625rem 0.75rem #b8c6db;
}

.search-btn {
  background-color: transparent;
  padding: 0.375rem 0.5625rem;
  margin-left: -3rem;
  border: none;
  color: #6c6c6c;
  transition: all 0.4s;
  z-index: 10;
}

.search-btn:hover {
  transform: scale(1.2);
  cursor: pointer;
  color: black;
}

.search-btn:focus {
  outline: none;
  color: black;
}
.feature-articles-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 0.0625rem solid;
  padding-bottom: 1.1563rem;
}
.feature-articles-title h2 {
  font-size: 2rem;
  font-weight: 500;
  line-height: 2.6875rem;
}
.feature-articles {
  max-width: 84.0625rem;
  margin: 0 auto;
  padding: 5.375rem 32px;
}
.article-safety {
  display: flex;
  gap: 2.9375rem;
  padding-top: 2.4375rem;
  padding-bottom: 2.4375rem;
}
.artical-safety-image {
  width: 45%;
}
.feature-articles .artical-safety-image:nth-child(2) {
  width: 55%;
}
.artical-safety-heading h4 {
  font-size: 1.125rem;
  font-weight: 300;
  line-height: 1.5rem;
  padding-bottom: 1.625rem;
}
.artical-safety-heading h2 {
  font-size: 2.5rem;
  font-weight: 500;
  padding-bottom: 1.375rem;
}
.artical-safety-heading p {
  font-size: 1.125rem;
  max-width: 656px;
  line-height: 1.5rem;
  padding-bottom: 1.375rem;
}
.artical-safety-heading p a {
  color: #000;
}
.artical-safety-heading h5 {
  font-size: 1.125rem;
  line-height: 1.5rem;
  font-weight: 300;
}
.artical-safety-heading h5 strong {
  font-weight: 700;
}
.mr-johson-box {
  background-color: rgba(255, 196, 12, 0.2);
  padding: 0.625rem;
  width: 33%;
  border-radius: 0.625rem;
}
.mr-johson .mr-johson-box:nth-child(2) {
  background-color: rgba(242, 101, 41, 0.2);
}
.mr-johson .mr-johson-box:nth-child(3) {
  background-color: rgba(16, 172, 227, 0.2);
}
.mr-johson-box-heading h4 {
  background-color: #ffc40c;
  border-radius: 0.8125rem;
  padding: 0.1875rem 1.0625rem;
  font-size: 0.875rem;
  font-weight: 300;
  margin-bottom: 0.6875rem;
  line-height: 1.1875rem;
  display: inline-block;
}
.mr-johson-box-heading h5 {
  font-size: 0.875rem;
  font-weight: 300;
  line-height: 1.1875rem;
  margin-bottom: 1.3125rem;
}
.mr-johson-box-heading p {
  font-weight: 1.125rem;
  font-weight: 500;
  max-width: 20.3125rem;
  line-height: 1.5rem;
}
.mr-johson {
  display: flex;
  gap: 2.5rem;
}
.recommended-artiicle .article-safety {
  box-shadow: inset 0rem 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1),
    0.1875rem 0.1875rem 0.375rem rgba(0, 0, 0, 0.1);
  padding: 0;
  align-items: center;
  margin-bottom: 2.5rem;
}
.article-safety .artical-safety-image:nth-child(1) {
  display: flex;
}

.recommended-artiicle .feature-articles-heading {
  margin-bottom: 2.4375rem;
  margin-top: 2.4375rem;
}
.recommended-artiicle .article-safety:last-child {
  margin-bottom: 0;
}
/* pagination */
.pagi-obgyn {
  text-align: center;
}

.pagination {
  display: inline-block;
}

.pagination a {
  color: black;
  float: left;
  padding: 0.5rem 1rem;
  text-decoration: none;
  transition: background-color 0.3s;
  font-size: 1.625rem;
}

.pagination a.active {
  background-color: #4caf50;
  color: white;
  border-radius: 5px;
}

.pagination a:hover:not(.active) {
  background-color: #ddd;
  border-radius: 5px;
}
.want-to-know {
  background-color: #1f2937;
  position: relative;
}
.want-to-know::before {
  position: absolute;
  content: "";
  bottom: -3px;
  left: 0;
  right: 0;
  background-image: url(../staticfiles/header-footer-line.webp);
  height: 10px;
  background-size: contain;
  background-repeat: no-repeat;
}
.want-to-know-services {
  display: flex;
  max-width: 90rem;
  margin: 0 auto;
  column-gap: 67px;
}
.want-to-know-image {
  width: 20%;
  padding: 3rem 0 3rem;
}
.want-to-know-heading {
  width: 55%;
  padding: 3rem 0 3rem;
}
.want-to-know-heading h3 {
  font-size: 2.5rem;
  line-height: 3.3125rem;
  font-weight: 500;
  color: #fff;
}
.want-to-know-heading h3 span {
  color: #ffc40c;
}
.want-to-know-heading h3 label {
  color: #c62328;
}
.want-to-know-heading h3 a {
  color: #f26529;
}
.ctaa-banner .ctaa-buttons img {
  width: 1.9375rem;
}
.ctaa-banner .ctaa-buttons a {
  display: flex;
  align-items: center;
  column-gap: 6.4px;
  background-color: #ffc40c;
  font-size: 2rem;
  padding: 0.575rem 2.5187rem;
}
.ctaa-banner-image {
  margin-top: -7.9375rem;
  margin-bottom: -0.25rem;
  padding: 0;
}
/* blog-post */
.blog-post-banner .banner-background {
  background-image: url(/staticfiles/blog-post-inner-banner.webp);
  height: 37.75rem;
}
.blog-social-icon {
  display: flex;
  gap: 0.6875rem;
  padding-left: 3rem;
}
.blog-all-inner {
  max-width: 87.5rem;
  margin: 0 auto;
  padding: 2.5625rem 1.25rem;
  display: flex;
  gap: 2.5rem;
}
.blog-inner-left-bar {
  width: 27.4%;
}
.blog-inner-right-bar {
  width: 63%;
}
.in-the-article {
  background-color: #edeeef;
  padding: 1.875rem 0.9375rem;
  border-radius: 10px;
}
.in-the-article ul {
  padding-left: 0px;
}
.in-the-article ul li {
  list-style: none;
  font-size: 0.875rem;
  line-height: 1.3875rem;
  font-weight: 200;
  display: flex;
  padding-bottom: 1.2rem;
}
.radio-round {
  appearance: none;
  background-color: #fff;
  border: 2px solid #1f2937;
  border-radius: 50%;
  cursor: pointer;
  width: 0.9375rem;
  height: 0.9375rem;
  position: relative;
  outline: none;
  margin-right: 10px;
}

.radio-round:checked {
  background-color: #1f2937; /* Change this to your desired color */
  border-color: #1f2937; /* Change this to your desired color */
}

.radio-round:checked::before {
  content: "";
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #1f2937;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.banner-wow {
  padding: 1.25rem;
  border-radius: 0.625rem;
  background: url(/staticfiles/blog-post-ctaa-banner.webp);
  background-repeat: no-repeat;
  background-size: cover;
  text-align: center;
  color: #fff;
  background-position: center;
  margin-top: 4.0625rem;
}
.logo img {
  width: unset;
}
.subtitle {
  font-size: 0.875rem;
  margin-bottom: 1.25rem;
}
.message {
  font-size: 1.125rem;
  margin-bottom: 0.9375rem;
  line-height: 1.5rem;
}
.phone {
  background-color: white;
  color: #ff7f50;
  padding: 0.625rem 1.625rem;
  border-radius: 0.3125rem;
  font-size: 1.25rem;
  font-weight: bold;
  display: inline-block;
}
.phone a {
  color: #ff7f50;
}
.phone img {
  width: 20px;
  margin-right: 8px;
  transform: translate(0px, 3px);
}
.blogpost-inner-heading p {
  margin-bottom: 1.1rem;
  line-height: 1.8rem;
}
.blogpost-inner-date {
  display: flex;
  justify-content: space-between;
  padding: 1.1875rem 0px;
}
.blogpost-inner-date span {
  font-size: 0.875rem;
}
.related-post .recommended-artiicle {
  max-width: 87.5rem;
  margin: 0 auto;
  padding: 0 1.25rem;
}
.all-related-post {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}
.related-post .article-safety {
  width: 31.8%;
  flex-direction: column;
  margin-bottom: 0;
  gap: 1.4375rem;
}
.related-post .artical-safety-image {
  width: 100%;
}
.related-post .artical-safety-heading h2 {
  font-size: 1.625rem;
  max-width: 20rem;
  line-height: 2.1875rem;
}
.date-mathew {
  display: flex;
  justify-content: space-between;
}
.related-post .artical-safety-heading {
  padding: 0 0.9375rem;
}
.all-related-post {
  padding-bottom: 11rem;
}
@media screen and (max-width: 1550px) {
  html {
    font-size: 14px;
  }
  .recommended-artiicle .article-safety {
    padding-right: 1rem;
  }
}
@media screen and (max-width: 1400px) {
  html {
    font-size: 12px;
  }
}
@media screen and (max-width: 1024px) {
  html {
    font-size: 9px;
  }
  .search-box {
    width: 36%;
  }
  .search-btn img {
    transform: translate(-10px, 0px);
    max-width: 80%;
  }
}
@media screen and (max-width: 790px) {
  html {
    font-size: 8px;
  }
  .recommended-artiicle .article-safety {
    flex-direction: column;
    padding-right: 0rem;
  }
  .artical-safety-image,
  .feature-articles .artical-safety-image:nth-child(2) {
    width: 100%;
  }
  .feature-articles .artical-safety-image:nth-child(2) .artical-safety-heading {
    padding: 2rem;
    padding-top: 0;
  }
  .recommended-artiicle .article-safety {
    width: 80%;
    margin: 0 auto;
    margin-bottom: 5rem;
  }
  .artical-safety-heading p {
    font-size: 1.525rem;
    line-height: 2.3rem;
  }
  .artical-safety-heading h2 {
    line-height: 3.5rem;
  }
  .feature-articles .artical-safety-image:nth-child(2) .artical-safety-heading a {
    font-weight: 700;
  }
  .want-to-know-services {
    padding-left: 4rem;
  }
  .want-to-know-heading {
    width: 62%;
  }
  .ctaa-banner-image {
    margin-top: -3.2375rem;
  }
}
@media screen and (max-width: 650px) {
  .article-safety {
    flex-direction: column;
  }
  .want-to-know-services {
    flex-direction: column;
    padding-left: 0;
  }
  .want-to-know-image.ctaa-banner-image {
    display: none;
  }
  .want-to-know-heading {
    margin: 0 auto;
    text-align: center;
    padding-bottom: 0;
  }
  .want-to-know-image {
    padding: 0rem 0 0rem;
    margin: 0 auto;
  }
  .want-to-know-heading .ctaa-buttons {
    justify-content: center;
  }
  .ctaa-banner .want-to-know {
    padding: 3rem 0px;
  }
  .feature-articles {
    padding: 86px 1rem;
  }
  .recommended-artiicle .article-safety {
    width: 100%;
  }
  .feature-articles {
    padding: 56px 2rem 3rem;
  }
  .feature-articles .artical-safety-image:nth-child(2) .artical-safety-heading {
    padding-left: 0;
    padding-right: 0;
  }
  .mr-johson {
    flex-wrap: wrap;
  }
  .mr-johson-box {
    width: 43%;
  }
}
