body {
  margin: 0;
}

.all-main h1,
.all-main h2,
.all-main h3,
.all-main h4,
.all-main h5,
.all-main h6,
.all-main p {
  margin: 0;
}

.all-main p {
  text-align: justify;
  font-size: 18px;
  font-weight: 400;
  line-height: 29px;
}

.all-main a {
  text-decoration: none;
}

.all-main {
  font-family: "Open Sans", sans-serif;
}

.all-main img {
  width: 100%;
}

.banner-background {
  background-image: url(/staticfiles/hughesnet-home-banner-image.webp);
  height: 675px;
  background-repeat: no-repeat;
  background-size: cover;
}

.hughsenet-section {
  display: flex;
  max-width: 1620px;
  margin: 0 auto;
  padding: 0 20px;
  height: 100%;
  align-items: center;
}

.hughsenet-section .hughsenet-heading {
  width: 45%;
}

.hughsenet-heading h1 {
  color: #000;
  font-size: 40px;
  font-weight: 700;
  line-height: 50px;
  max-width: 620px;
}

.hughsenet-heading label {
  color: #0091c4;
  font-size: 30px;
}

.hughsenet-heading span {
  color: #ff5820;
}

.hughsenet-heading-list {
  margin-top: 50px;
  height: 42%;
}

.hughsenet-heading-list h3 {
  display: flex;
  column-gap: 9px;
  padding-bottom: 8px;
}

.hughsenet-heading-list h3 span {
  color: #000;
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
}

.hughsenet-heading-list h3 span img {
  transform: translate(0px, 2px);
}

.hughsenet-heading p {
  margin-top: 5px;
  max-width: 600px;
}

.main-button {
  display: flex;
  column-gap: 28px;
}

.main-button a {
  display: flex;
  border-radius: 10px;
  background: #0091c4;
  color: #fff;
  font-size: 20px;
  font-weight: 700;
  line-height: 20px;
  padding: 13px 16px;
  align-items: center;
  column-gap: 8px;
  margin-top: 40px;
}

.main-button a img {
  width: unset;
}

.main-button .call-icon {
  background-color: #ff5820;
}

/* online fast image */

.online-fast-image {
  background-image: url(/staticfiles/hughesnet-home-online-fast-image.webp);
  height: 202px;
}

.all-online-fast {
  display: flex;
  max-width: 1620px;
  padding: 20px 20px 30px;
  margin: 0 auto;
  column-gap: 50px;
  align-items: center;
}

.all-online-fast .online-fast-heading h2 {
  color: #fff;
  font-size: 36px;
  font-weight: 700;
}

.all-online-fast .online-fast-heading h3 {
  color: #fff;
  font-size: 20px;
  font-weight: 700;
  margin-top: 19px;
}

.all-online-buttn {
  border: 5px solid #ffffff;
  padding: 8px 10px;
  border-radius: 17px;
}

.all-online-buttn a {
  display: flex;
  column-gap: 16px;
  color: #fff;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px;
  border-radius: 10px;
  background: #ff5820;
  padding: 13px 9px;
  align-items: center;
}

.all-online-buttn a img {
  width: unset;
}

.online-fast-heading-cta {
  position: relative;
}

.arrow-container {
  position: relative;
  top: -43px;
}

.arrow {
  position: absolute;
  top: 0px;
  left: -156%;
  transform: translateX(50%);
  animation: arrowAnimation 5s ease-in-out;
}

@keyframes arrowAnimation {
  0% {
    top: -50px;
    transform: translateX(-50%);
  }

  50% {
    top: 0px;
    transform: translateX(50%) rotate(0deg);
  }

  100% {
    top: 0px;
    transform: translateX(50%);
  }
}

.online-fast-image p {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.glace-1 {
  display: flex;
  max-width: 1620px;
  padding: 0 20px;
  justify-content: space-between;
  margin: 0 auto;
  align-items: end;
}

.glace-1 .glace-heading {
  width: 38%;
}

.glace-heading-image {
  width: 54%;
}

.at-a-glance {
  padding: 78px 0px;
  background: #f9f9f9;
}

.glace-heading h2 {
  color: #000;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
}

.glace-heading h2 span {
  color: #0091c4;
}

.hughsnet-plan-heading {
  text-align: center;
}

.hughsnet-plan-heading h2 {
  font-size: 32px;
  font-weight: 600;
}

.hughsnet-plan-heading h2 span {
  color: #0091c4;
}

.hughsnet-plan-heading h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 14px 0px 28px;
}

.hughsnet-plan-heading p {
  text-align: center;
  font-size: 13.8px;
  font-weight: 500;
  max-width: 1555px;
  margin: 0 auto;
}

.hughsnet-plan-background {
  background-image: url(/staticfiles/hughsenet-home-page-hughsnet-plan-background.webp);
  background-repeat: no-repeat;
  margin-top: 52px;
  background-position: bottom;
}

.hughsnet-cards {
  max-width: 1320px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  column-gap: 30px;
}

.hughsnet-card1 {
  border-radius: 4px;
  border: 3px solid #0091c4;
  background: #fff;
  padding: 22px 15px 18px;
  width: 20.6%;
}

.starter-plan {
  display: flex;
  column-gap: 25px;
  align-items: center;
}

.starter-plan h3 {
  color: #0091c4;
  font-size: 30px;
  font-weight: 700;
  line-height: 0.8;
}

.starter-plan h3 span {
  font-size: 16px;
  color: #000;
}

.starter-plan h4 {
  color: #ff5820;
  font-size: 50px;
  font-weight: 700;
  line-height: 50px;
  margin-top: -28px;
}

.month-plan .month-plan-prices {
  border-radius: 5px;
  background: #ff5820;
  padding: 6px 9px;
  width: 129px;
  height: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
}

.month-plan .month-plan-prices:nth-child(2) {
  background-color: #2caa00;
}

.month-plan .month-plan-prices h3 {
  color: #fff;
  text-align: center;
  font-size: 32px;
  font-weight: 700;
}

.month-plan .month-plan-prices h3:nth-child(2) {
  font-size: 11px;
  font-weight: 700;
}

.month-plan .month-plan-prices:nth-child(2) h3:nth-child(2) {
  font-size: 20px;
}

.month-decide {
  font-size: 25px;
}

.month-plan .month-plan-prices:nth-child(2) h3:nth-child(1) {
  font-size: 35px;
}

.month-plan .month-plan-prices:nth-child(2) h3:nth-child(1) sup {
  font-size: 20px;
  margin-left: 5px;
}

.month-plan {
  display: flex;
  column-gap: 12px;
  margin-top: 8px;
}

.speed-plans {
  background-color: #0091c4;
  display: flex;
  justify-content: space-between;
  padding: 2px 13px 7px;
  border-radius: 5px;
  align-items: center;
}

.speed-plans span:nth-child(1) {
  font-size: 22px;
  font-weight: 700;
  line-height: 41px;
  text-align: center;
  color: #ffffff;
}
.speed-plans span label:nth-child(1) {
  font-size: 16px;
}
.speed-plans span:nth-child(2) {
  color: #fff;
  font-size: 40px;
  font-weight: 700;
  line-height: 50px;
}

.speed-plans span:nth-child(2) sup,
.speed-plans span:nth-child(2) sub {
  font-size: 18px;
  font-weight: 600;
}

.satellite-plan h4 {
  color: #ff5820;
  font-size: 16px;
  font-weight: 700;
  padding: 8px 0px;
}

.hughsenet-heading-list.plans-listing h3 span {
  color: #000;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 21px;
}

.hughsenet-heading-list.plans-listing img {
  width: unset;
}

.hughsenet-heading-list.plans-listing h3 {
  padding-bottom: 10px;
  max-width: 268px;
}

.starter-plan h4 sup {
  font-size: 20px;
}

.plan-button {
  justify-content: center;
  align-items: center;
}

.plan-button a {
  font-size: 16px;
  margin-top: 0px;
}

.plan-button .call-icon {
  padding: 11px 9px;
}

.hughsnet-plan-section {
  padding: 35px 0px 63px;
}

.plan-bottom-lines {
  padding: 37px 0 24px;
}

.plan-bottom-lines p {
  text-align: center;
  font-size: 12px;
  font-weight: 700;
}

.plans-paragraph p,
.plans-paragraph h4,
.plans-paragraph ul li {
  font-size: 12px;
}

.plans-paragraph {
  max-width: 1324px;
  margin: 0 auto;
  padding: 0 20px;
}

.plans-paragraph p {
  line-height: 1.4;
}

.get-your-plan .online-fast-image {
  background-image: url(/staticfiles/hughesnet-home-online-fast-image2.webp);
  background-size: cover;
}

.get-your-plan .all-online-buttn a {
  background-color: #0091c4;
}

.get-your-plan .all-online-fast {
  justify-content: end;
}

.get-your-plan .all-online-fast .online-fast-heading h3 {
  text-align: right;
}

.fusion-card1 {
  border-color: #ff5820;
}

.fusion-card1 .starter-plan h3 {
  color: #ff5820;
}

.fusion-hugshnet-plan .fusion-card1 .starter-plan h3 {
  line-height: 37px;
}

.hughsnet-package-services {
  max-width: 960px;
  margin: 0 auto;
  column-gap: 30px;
}

.hughsnet-phone-services,
.free-equipment,
.discount-for-three,
.free-equipment {
  display: flex;
  align-items: center;
}

.hughsnet-phone-services {
  column-gap: 13px;
}

.hughsnet-package-services-card1 p {
  text-align: left;
}

.hughsnet-package-services-card1 {
  border-radius: 8px;
  border: 3px solid #0091c4;
  background: #fff;
  padding: 15px;
}

.hughsnet-package-services-card2 {
  border-radius: 5px;
  border: 3px solid #ff5820;
  background: #fff;
  padding: 18px;
  margin-bottom: 15px;
}

.hughsnet-package-services-card3 {
  background-image: url(/staticfiles/mastercard-image.png);
  text-align: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25px 0 30px;
  background-position: center;
}

.hughsnet-package-services-card2 p {
  text-align: center;
}

.hughsnet-package-services-card3 .hughsnet-bonus-zone h4 {
  color: #0091c4;
  font-size: 15px;
}

.hughsnet-package-services-card3 .hughsnet-bonus-zone p {
  max-width: 233px;
  margin: 0 auto;
  color: #ff5820;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
}

.discount-for-three,
.free-equipment {
  justify-content: space-between;
}

.discount-for-three {
  margin-top: 13px;
  position: relative;
}

.hughsnet-package-services .discount-text {
  position: absolute;
  right: 0;
  bottom: -27px;
}

.hughsnet-phone-services h4 {
  color: #000;
  font-size: 22px;
  font-weight: 600;
  padding-bottom: 5px;
}

.hughsnet-phone-services h4 img {
  transform: translate(0px, 3px);
}

.hughsnet-phone-services h4 span {
  color: #0091c4;
  font-weight: 700;
}

.hughsnet-phone-services h4:nth-child(2) {
  max-width: 217px;
}

.hughsnet-phone-services {
  align-items: start;
}

.hughsnet-phone-services p {
  line-height: normal;
}

.free-equipment h4 {
  color: #000;
  font-size: 24px;
  font-weight: 700;
  line-height: 37px;
  max-width: 215px;
}

.discount-for-three h4 {
  color: #000;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 15px;
}

.discount-for-three h4 label {
  color: #0091c4;
  font-weight: 700;
}

.free-equipment label {
  color: #ff5820;
}

.hughsnet-bonus-zone h4 {
  color: #ff5820;
  font-size: 30px;
  font-weight: 700;
}

/* intro-fusion */
.intro-fusion .glace-1 {
  display: flex;
  max-width: 1747px;
  padding: 0 0px;
  margin: 0 0 0 auto;
  align-items: center;
}

.intro-fusion .glace-heading h2 {
  color: #ff5820;
}

.intro-fusion .glace-heading h2 span {
  font-weight: 700;
}

.intro-fusion {
  padding: 0;
  background: #f9f9f9;
}

.intro-fusion .glace-1 .glace-heading {
  width: 36%;
}

.intro-fusion .main-button a {
  background: #ff5820;
  margin-top: 40px;
}

.fusion-hugshnet-plan .hughsnet-plan-background {
  background: url(/staticfiles/hughsnet-home-page-fusion-section-background-image.webp);
  margin-top: 0px;
  background-repeat: no-repeat;
}

.fusion-hugshnet-plan .hughsnet-cards {
  max-width: 1350px;
  flex-wrap: wrap;
  row-gap: 20px;
}
.fusion-hugshnet-plan .hughsnet-cards .hughsnet-card1 {
  width: 21.6%;
}
.fusion-card1 .equipment-less {
  height: 10%;
}
.starter-plan sup {
  font-size: 18px;
}

.fusion-hugshnet-plan .plan-button a {
  margin-top: 20px;
}

.fusion-hugshnet-plan .hughsnet-package-services {
  margin-top: 20px;
}

.fusion-hugshnet-plan .hughsnet-package-services-card2 {
  margin-top: 20px;
}

.fusion-hugshnet-plan .hughsnet-package-services-card2 {
  width: 50%;
  margin: 14px auto;
  text-align: center;
}

a.call-icon.business-call {
  margin-top: 28px;
}

.equipment-less p {
  color: #000;
  font-size: 12px;
  font-weight: 400;
  line-height: normal;
  margin: 5px 0px 5px;
}

.discount-text span {
  color: #000;
  font-size: 16px;
  font-weight: 600;
}

.discount-text strike {
  color: #ff5820;
  font-size: 16px;
}

.discount-text strike span {
  font-size: 16px;
  font-weight: 600;
}

.discount-text {
  display: flex;
  align-items: center;
  margin-top: 8px;
  justify-content: end;
}

.inter-sufring-listing ul li {
  color: #000;
  font-size: 18px;
  font-weight: 400;
  line-height: 30px;
}

.lot-more-value {
  padding: 0;
  background: transparent;
}

.lot-more-value .at-all-glace {
  background: url(/staticfiles/hughsnet-home-page-lot-more-section.webp);
  height: 611px;
  background-size: cover;
  background-repeat: no-repeat;
}

.lot-more-value .glace-1 {
  height: 100%;
  align-items: center;
}

.inter-sufring-listing h3 {
  font-size: 20px;
  font-weight: 600;
}

.inter-sufring-listing p {
  font-size: 18px;
  font-weight: 400;
  max-width: 450px;
}

.lot-more-value .glace-1 .glace-heading {
  width: 42%;
}

/* faqs-question */
.faqs-container {
  margin: 0 auto;
  max-width: 1264px;
  padding: 0 20px;
}

.accordion {
  display: flex;
  flex-wrap: wrap;
  column-gap: 18px;
}

.accordion .accordion-item {
  width: 44%;
  border-radius: 10px;
  background: #f5f5f5;
  box-shadow: 0px 5px 16px 0px rgba(8, 15, 52, 0.06);
  padding: 16px 32px;
  margin-bottom: 20px;
}

.accordion button {
  position: relative;
  display: block;
  text-align: left;
  width: 100%;
  border: none;
  background: none;
  outline: none;
  font-weight: 500;
  line-height: 28px;
  color: #242424;
  font-size: 22px;
  font-weight: 500;
}

.accordion button:hover,
.accordion button:focus {
  cursor: pointer;
  color: #000;
}

.accordion button:hover::after,
.accordion button:focus::after {
  cursor: pointer;
  color: #000;
  border: 1px solid #03b5d2;
}

.accordion button .icon {
  display: inline-block;
  position: absolute;
  top: -6px;
  right: 0;
  width: 41px;
  height: 41px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #0091c4;
  display: none;
}

.accordion button .icon::before {
  display: block;
  position: absolute;
  content: "";
  top: 17px;
  left: 10px;
  width: 20px;
  height: 3px;
  background-color: #fff;
}

.accordion button .icon::after {
  display: block;
  position: absolute;
  content: "";
  top: 10px;
  left: 18px;
  width: 3px;
  height: 18px;
  background-color: #fff;
}

.accordion button[aria-expanded="true"] .icon::after {
  width: 0;
}

.accordion button[aria-expanded="true"] + .accordion-content {
  opacity: 1;
  max-height: unset;
  transition: all 200ms linear;
  will-change: opacity, max-height;
}

.accordion .accordion-content {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: opacity 200ms linear, max-height 200ms linear;
  will-change: opacity, max-height;
}

.accordion .accordion-content p {
  color: #5e5e5e;
  font-size: 18px;
  font-weight: 400;
  line-height: 30px;
  margin-top: 20px;
}

.accordion-title strong {
  color: #0091c4;
  font-size: 22px;
  font-weight: 700;
  line-height: 28px;
}

.faqs-container h2 {
  color: #000;
  font-size: 32px;
  font-weight: 600;
  line-height: 30px;
  text-align: center;
  margin-bottom: 75px;
}

.Faqs-section {
  padding: 89px 0px;
}

.hughsnet-plan-heading h2 label {
  color: #ff5820;
}

.internet-services .hughsnet-plan-heading p {
  text-align: left;
  font-weight: 500;
  line-height: 25px;
  max-width: 1400px;
  margin-top: 25px;
  padding: 0 20px;
}

.hughsnet-services-background {
  display: flex;
  max-width: 1420px;
  margin: 0 auto;
  padding: 0 20px;
  justify-content: space-between;
  align-items: center;
  margin-top: 27px;
  column-gap: 22px;
}

.hughsnet-services-card1 {
  width: 24%;
  padding-bottom: 70px;
}

/* .hughsnet-services-card2 {
  width: 40%;
} */

.hughsnet-services-card3 {
  width: 25%;
  padding-bottom: 70px;
}

.high-speed-connectivity {
  display: flex;
  border-radius: 46.5px 0px 0px 46.5px;
  background: #fff;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  padding: 11px;
}

.hugsnet-content h4 {
  color: #0091c4;
  text-align: right;
  font-size: 14px;
  font-weight: 600;
}

.hugsnet-content p {
  color: #000;
  font-size: 13px;
  font-weight: 400;
  text-align: right;
  line-height: normal;
  max-width: 330px;
}

.high-speed-connectivity {
  margin-bottom: 70px;
  justify-content: space-between;
  align-items: center;
}

.high-speed-connectivity:nth-child(4) {
  margin-bottom: 0;
}

.ligthen-fast .hugsnet-content h4 {
  text-align: left;
}

.ligthen-fast .hugsnet-content p {
  text-align: left;
}

.hughsnet-services-card2 img {
  transform: translate(0px, 5px);
}

.ligthen-fast .high-speed-connectivity {
  border-radius: 0px 46.5px 46.5px 0px;
  background: #fff;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

@media only screen and (max-width: 1600px) {
  .hughsenet-section,
  .all-online-fast,
  .glace-1,
  .hughsnet-plan-heading p {
    max-width: 1220px;
  }

  .intro-fusion .glace-1 {
    max-width: 1339px;
  }

  .hughsenet-heading h1 {
    font-size: 36px;
  }

  .all-online-fast .online-fast-heading h2 {
    font-size: 32px;
  }

  .glace-1 {
    align-items: center;
  }

  .glace-heading-image {
    width: 50%;
  }

  .glace-1 .glace-heading {
    width: 45%;
  }

  .all-main p {
    font-size: 16px;
  }

  .glace-heading h2 {
    font-size: 25px;
  }

  .intro-fusion .glace-1 .glace-heading {
    width: 40%;
  }

  .hugsnet-content p {
    font-size: 13px;
  }
}

@media only screen and (max-width: 1440px) {
}

@media only screen and (max-width: 1340px) {
  .hughsenet-section .hughsenet-heading {
    width: 55%;
  }

  .hughsenet-section .hughsenet-heading:nth-child(2) {
    width: 45%;
  }

  .hughsenet-section,
  .all-online-fast,
  .glace-1,
  .hughsnet-plan-heading p {
    max-width: 1020px;
  }

  .all-online-fast .online-fast-heading h2 {
    font-size: 26px;
  }

  .all-online-fast .online-fast-heading h3 {
    font-size: 14px;
  }

  .glacing-hughsnet .glace-heading-image img {
    height: 500px;
    object-fit: cover;
  }

  .intro-fusion .glace-1 {
    max-width: 1101px;
  }

  /* .intro-fusion{
        padding: 70px 0px;
    } */
  .lot-more-value .glace-1 .glace-heading {
    width: 54%;
  }

  .inter-sufring-listing ul li {
    font-size: 16px;
  }

  .intro-fusion .glace-1 .glace-heading {
    width: 44%;
  }

  .lot-more-value .at-all-glace {
    background: url(/staticfiles/hughsnet-home-page-lot-more-section.webp);
    height: 449px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }

  .lot-more-value .glace-1 .glace-heading {
    width: 100%;
  }

  .main-button a {
    margin-top: 7px;
  }

  .inter-sufring-listing ul li {
    font-size: 14px;
  }

  .hughsnet-plan-section .hughsnet-cards {
    flex-wrap: wrap;
  }

  .hughsnet-card1 {
    width: 24.6%;
  }

  .hughsenet-heading-list {
    height: 33%;
  }

  .hughsnet-package-services-card3 {
    margin: 20px 0px;
  }

  .hughsnet-package-services-card2 {
    width: 50%;
    margin: 14px auto;
    text-align: center;
  }

  .plan-bottom-lines p {
    padding: 0 20px;
  }
}

@media only screen and (max-width: 1139px) {
  .banner-background {
    background-position: bottom;
  }

  .hughsenet-heading h1 {
    font-size: 34px;
  }

  .all-online-fast .online-fast-heading h2 {
    font-size: 24px;
  }

  .hughsenet-section,
  .all-online-fast,
  .glace-1,
  .hughsnet-plan-heading p {
    max-width: 940px;
  }

  .all-online-fast {
    column-gap: 259px;
  }

  .intro-fusion .glace-1 {
    max-width: 980px;
  }

  .glace-heading p {
    font-size: 14px;
  }

  .glace-heading h2 {
    font-size: 22px;
    margin-bottom: 10px;
  }

  .intro-fusion .main-button a {
    margin-top: 18px;
  }

  .main-button a {
    font-size: 16px;
    padding: 8px 10px;
  }

  .intro-fusion .glace-1 .glace-heading {
    width: 48%;
  }

  .intro-fusion {
    padding: 33px 0px;
  }

  .fusion-hugshnet-plan .hughsnet-plan-background {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .accordion .accordion-item {
    width: 42%;
  }

  .online-fast-image p {
    font-size: 14px;
  }
}

@media only screen and (max-width: 899px) {
  .hughsenet-section,
  .all-online-fast,
  .glace-1 {
    flex-direction: column;
  }

  .hughsenet-section .hughsenet-heading {
    width: 100%;
  }

  .banner-background {
    background-position: unset;
  }

  .banner-section .hughsenet-section {
    justify-content: center;
  }

  .all-online-fast {
    padding: 0px 20px;
    row-gap: 20px;
  }

  .online-fast-image {
    height: unset;
    padding: 20px 0px;
  }

  .all-online-buttn a {
    font-size: 16px;
    padding: 6px 6px;
  }

  .all-online-buttn {
    padding: 4px 4px;
  }

  .online-fast-image p {
    max-width: 500px;
    margin: 10px auto 0;
  }

  .arrow {
    position: absolute;
    left: -229%;
  }

  .glace-1 .glace-heading,
  .glace-heading-image,
  .intro-fusion .glace-1 .glace-heading {
    width: 100%;
  }

  .glacing-hughsnet .glace-heading-image img {
    height: unset;
  }

  .glacing-hughsnet .glace-1 {
    row-gap: 30px;
  }

  .at-a-glance {
    padding: 40px 0px;
  }

  .hughsnet-plan-heading p {
    max-width: 700px;
    font-size: 14px;
  }

  .hughsnet-cards {
    max-width: 700px;
  }

  .starter-plan h4 {
    font-size: 27px;
  }

  .starter-plan h3 {
    font-size: 24px;
  }

  .starter-plan {
    column-gap: 10px;
  }

  .starter-plan h3 span {
    font-size: 12px;
  }

  .month-plan .month-plan-prices:nth-child(2) h3:nth-child(1) {
    font-size: 30px;
  }

  .month-plan .month-plan-prices h3 {
    font-size: 25px;
  }

  .month-plan {
    row-gap: 10px;
    flex-direction: column;
  }

  .hughsnet-card1 {
    width: 27%;
  }

  .month-plan .month-plan-prices {
    width: unset;
    height: unset;
  }

  .hughsenet-heading-list.plans-listing h3 span {
    font-size: 13px;
  }

  .get-your-plan .online-fast-image {
    background-position: top;
  }

  .intro-fusion .glace-1 .glace-heading {
    max-width: 700px;
  }

  .glace-1 {
    row-gap: 30px;
  }

  .lot-more-value .glace-1 {
    justify-content: center;
    row-gap: 0;
  }

  .accordion .accordion-item {
    width: 70%;
    margin: 15px auto;
  }

  .Faqs-section {
    padding: 20px 0px;
  }
}

@media only screen and (max-width: 767px) {
  .hughsenet-heading h1 {
    font-size: 30px;
    line-height: 41px;
  }

  .hughsenet-section {
    padding: 0 25px;
  }

  .all-main p {
    font-size: 14px;
  }

  .banner-section .hughsenet-section p {
    line-height: 20px;
  }

  .hughsenet-heading-list h3 span {
    font-size: 13px;
    line-height: 24px;
  }

  .online-fast-heading {
    text-align: center;
  }

  .all-online-fast .online-fast-heading h2 {
    font-size: 30px;
  }

  .all-online-fast .online-fast-heading h3 {
    max-width: 300px;
    margin: 14px auto 0px;
  }

  .main-button a {
    font-size: 14px;
    padding: 9px 8px;
  }

  .main-button {
    display: flex;
    column-gap: 15px;
  }

  .all-online-fast {
    row-gap: 39px;
  }

  .free-paragraph p {
    font-size: 10px;
    line-height: normal;
    max-width: 364px;
    margin-top: 37px;
  }

  .glace-heading h2 {
    text-align: center;
  }

  .glace-heading p {
    line-height: 20px;
  }

  .hughsenet-heading-list h3 span {
    font-size: 14px;
  }

  .hughsnet-cards {
    flex-direction: column;
    row-gap: 20px;
  }

  .hughsnet-plan-background,
  .fusion-hugshnet-plan .hughsnet-plan-background {
    background-image: unset;
    height: unset;
    margin-top: 24px;
  }

  .hughsnet-card1,
  .fusion-hugshnet-plan .hughsnet-card1 {
    width: 67.9%;
    margin: 0 auto;
  }

  .fusion-hugshnet-plan .hughsnet-cards {
    margin-top: 24px;
  }

  .month-plan {
    flex-wrap: nowrap;
    flex-direction: row;
  }

  .month-plan .month-plan-prices {
    width: 48%;
  }

  .starter-plan {
    column-gap: 30px;
  }

  .starter-plan h4 {
    font-size: 50px;
  }

  .starter-plan h3 {
    font-size: 30px;
  }

  .hughsnet-plan-heading h2 {
    font-size: 22px;
  }

  .hughsnet-plan-heading h3 {
    font-size: 16px;
    margin: 6px 0px 10px;
  }

  .hughsnet-plan-heading p {
    font-size: 14px;
    text-align: justify;
    max-width: 349px;
  }

  .get-your-plan .all-online-fast .online-fast-heading h3 {
    text-align: center;
  }

  .hughsnet-plan-section {
    padding: 35px 0px 80px;
  }

  .get-your-plan .all-online-fast {
    flex-direction: column-reverse;
  }

  .intro-fusion .glace-heading h2 {
    max-width: 300px;
    text-align: left;
    line-height: normal;
  }

  .at-a-glance {
    padding: 30px 25px;
  }

  .intro-fusion .glace-heading-image {
    display: none;
  }

  .hughsnet-plan-section.fusion-hugshnet-plan {
    padding: 35px 0px 40px;
  }

  .fusion-hugshnet-plan .hughsnet-plan-heading p {
    text-align: center;
    line-height: normal;
  }

  .lot-more-value {
    margin-top: 60px;
  }

  .lot-more-value .at-all-glace {
    height: unset;
    background-image: unset;
  }

  .hughsnet-services-background {
    flex-direction: column;
  }

  .hughsnet-services-card1,
  .hughsnet-services-card3 {
    width: 100%;
  }
  .fusion-hugshnet-plan .hughsnet-cards .hughsnet-card1 {
    width: 70%;
  }
  .hughsnet-package-services-card1 {
    width: 79%;
    margin: 0 auto;
  }
  .accordion .accordion-item {
    width: 84%;
    margin: 15px auto;
  }
  .accordion .accordion-content p {
    font-size: 14px;
    line-height: 25px;
  }
  .main-button {
    justify-content: center;
  }
  .faqs-container h2 {
    font-size: 27px;
    line-height: 35px;
    margin-bottom: 20px;
  }
  .faqs-container h2 {
    line-height: 35px;
  }
  .ligthen-fast .hugsnet-content p {
    margin-top: 4px;
  }
  .high-speed-connectivity img {
    max-width: 71%;
    transform: translate(6px, 8px);
  }
  .high-speed-connectivity div {
    text-align: center;
  }
  .high-speed-connectivity {
    margin-bottom: 20px;
  }
  .hughsnet-services-card3 {
    margin-top: 20px;
    padding-bottom: 50px;
  }
  .intro-fusion .glace-heading h2 {
    max-width: unset;
  }
}
