:root {
  --color-primary: #0072ab;
  --color-secondary: #c62328;
  --color-tertiary: #f26529;
  --color-quaternary: #10ace3;
  --color-quinary: #408a13;
  --color-tertiary: #ffc40c;
  --color-gray: #58595b;
  --color-white: #fff;
  --color-black: #000;
}

.hughsenet-section {
  display: flex;
  max-width: 1320px;
  margin: 0 auto;
  height: 100%;
  align-items: center;
}

.hughsenet-section .hughsenet-heading {
  width: 45%;
}

.hughsenet-heading h1 {
  color: #000;
  font-size: 40px;
  font-weight: 700;
  line-height: 50px;
  max-width: 620px;
  font-family: var(--font-ars-maquette-light);
  font-weight: 300;
}

.hughsenet-heading label {
  color: var(--color-secondary);
  font-size: 30px;
}

.hughsenet-heading span {
  color: #ff5820;
}

.hughsenet-heading-list {
  margin-top: 13px;
}

.hughsenet-heading-list h3 {
  display: flex;
  column-gap: 9px;
  padding-bottom: 8px;
}

.hughsenet-heading-list h3 span {
  color: #000;
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
}

.hughsenet-heading-list h3 span img {
  transform: translate(0px, 2px);
}

.hughsenet-heading p {
  margin-top: 5px;
  max-width: 616px;
  font-family: var(--font-ars-maquette-light);
  font-size: 18px;
  font-weight: 300;
  line-height: 29px;
}

.main-button {
  display: flex;
  column-gap: 28px;
}

.main-button a {
  display: flex;
  border-radius: 10px;
  background: #0091c4;
  color: #fff;
  font-size: 20px;
  font-weight: 700;
  line-height: 20px;
  padding: 13px 16px;
  align-items: center;
  column-gap: 8px;
  margin-top: 40px;
}

.main-button a img {
  width: unset;
}

.main-button .call-icon {
  background-color: #ff5820;
}

.business-banner-section .hughsenet-heading span {
  color: var(--color-secondary);
}

.business-banner-section .banner-background {
  height: 672px;
  background: url(/staticfiles/wow-business-page-banner-image.webp);
  background-size: cover;
}

/* business form */
.business-banner-section .hughsenet-section {
  justify-content: space-between;
}

.business-banner-section .hughsenet-heading1 {
  max-width: 29.4%;
}

.top-bar {
  background-color: var(--color-secondary);
  font-family: var(--font-ars-maquette-medium);
  font-weight: 500;
  color: #fff;
  font-size: 24px;
  line-height: 45px;
  padding: 15px 0px;
  display: flex;
  align-items: center;
  column-gap: 16px;
  justify-content: center;
}

.top-bar img {
  width: unset;
}

.form-container {
  padding: 20px;
  background-color: #fff;
}

.form-field {
  margin-bottom: 10px;
}

.form-field input {
  width: 96%;
  padding: 13px 0px;
  border-radius: 3px;
  border: 0.5px solid #8d8d8d;
  background: #f9f9f9;
  color: #000;
  font-size: 14px;
  font-weight: 300;
  line-height: 18px;
  padding-left: 16px;
  font-family: var(--font-ars-maquette-light);
}

.submit-button {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  line-height: 18px;
  width: 100%;
  padding: 13px 0px;
  background-color: var(--color-secondary);
  border: none;
}

.form-container p {
  color: #000;
  text-align: justify;
  font-size: 12px;
  font-weight: 300;
  font-family: var(--font-ars-maquette-light);
  line-height: 18px;
  margin-top: 37px;
}

.business-package-background {
  background-image: url(/staticfiles/wow-business-page-package-card.webp);
  height: 803px;
  padding-top: 98px;
  background-repeat: no-repeat;
  background-size: cover;
}

.package-cards-items {
  display: flex;
  max-width: 1024px;
  margin: 0 auto;
  justify-content: space-between;
}

.package-cards-item1 {
  background: #fff;
  box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.1);
  padding: 21px;
  width: 26%;
}

.internet-card-heading {
  display: flex;
  align-items: center;
  column-gap: 20px;
  margin-bottom: 22px;
}

.internet-card-heading span {
  color: #0091c4;
  font-size: 30px;
  font-weight: 300;
  font-family: var(--font-ars-maquette-light);
  line-height: 30px;
}

.package-cards-item1 p,
.package-cards-item1 ul li {
  color: #000;
  text-align: justify;
  font-size: 15px;
  font-style: normal;
  font-weight: 300;
  font-family: var(--font-ars-maquette-light);
  line-height: 20px;
}

.cards-paragraph p {
  color: #fff;
  text-align: justify;
  font-size: 14px;
  line-height: 25px;
  margin: 0 auto;
  margin-top: 70px;
  max-width: 1563px;
  padding: 0 20px;
  font-family: var(--font-ars-maquette-regular);
}

.hughsnet-internet-plans .all-online-fast .online-fast-heading h2 {
  font-size: 32px;
}

.internet-avilable-my-area {
  background-color: transparent;
}

.internet-avilable-my-area .at-all-glace {
  background: url(/staticfiles/hughsnet-business-page-internet-services-my-area-image.webp);
}

.my-area-services p {
  max-width: 670px;
}

.internet-avilable-my-area .glace-1 .glace-heading {
  width: 41%;
}

.services-availability {
  padding: 64px 0px;
}

@media only screen and (max-width: 1550px) {
  .hughsenet-section {
    max-width: 1220px;
    padding: 0 20px;
  }
}

@media only screen and (max-width: 1350px) {
  .business-banner-section .hughsenet-section .hughsenet-heading {
    width: 48%;
  }

  .business-banner-section .hughsenet-heading1 {
    max-width: 41.4%;
  }

  .package-cards-items {
    max-width: 850px;
  }

  .package-cards-item1 p,
  .package-cards-item1 ul li {
    font-size: 13px;
  }

  .internet-card-heading span {
    font-size: 22px;
  }

  .cards-paragraph p {
    font-size: 13px;
    margin-top: 27px;
  }

  .hughsnet-internet-plans .all-online-fast .online-fast-heading h2 {
    font-size: 25px;
  }

  .internet-avilable-my-area .glace-1 .glace-heading {
    width: 51%;
  }

  .my-area-services p {
    max-width: 451px;
  }

  .lot-more-value.internet-avilable-my-area .at-all-glace {
    height: unset;
    background-size: cover;
    background-repeat: no-repeat;
    padding: 25px 0;
    background-position: top;
  }
}

@media only screen and (max-width: 941px) {
  .business-banner-section .hughsenet-section {
    flex-direction: row;
  }

  .business-banner-section .hughsenet-heading h1 {
    font-size: 30px;
    line-height: 40px;
  }

  .all-main p {
    font-size: 14px;
  }

  .top-bar {
    font-size: 16px;
    padding: 6px 0px;
  }

  .form-field input {
    width: 93%;
  }

  .business-package-background {
    padding-top: 60px;
  }

  .package-cards-items {
    max-width: 714px;
  }

  .internet-card-heading span img {
    max-width: 70%;
    transform: translate(0px, 4px);
  }

  .internet-card-heading {
    column-gap: 0px;
    margin-bottom: 8px;
  }

  .internet-card-heading p {
    font-size: 13px;
    line-height: normal;
  }

  .cards-paragraph p {
    font-size: 13px;
    line-height: normal;
  }

  .business-package-background {
    height: 678px;
  }

  .hughsnet-internet-plans .all-online-fast .online-fast-heading h2 {
    text-align: center;
  }

  .internet-avilable-my-area .glace-1 .glace-heading {
    width: 100%;
  }

  .internet-avilable-my-area .glace-heading h2 {
    max-width: 400px;
  }

  .internet-avilable-my-area {
    padding: 0 0;
  }
}

@media only screen and (max-width: 767px) {
  .business-banner-section .banner-background {
    background-image: url(/staticfiles/wow-internet-page-banner-image-mobile-view.webp);
    height: 1189px;
    padding-top: 10px;
  }

  .business-banner-section .hughsenet-section {
    padding: 0;
  }

  .business-package-cards .business-package-background {
    height: unset;
    background-image: url(/staticfiles//wow-business-page-package-card-mobile-responsive.webp);
    padding-bottom: 40px;
  }

  .business-banner-section .hughsenet-section {
    flex-direction: column;
    height: 96%;
  }

  .business-banner-section .hughsenet-section .hughsenet-heading,
  .business-banner-section .hughsenet-heading1 {
    width: 100%;
    max-width: 100%;
  }

  .package-cards-items {
    flex-direction: column;
    row-gap: 20px;
  }

  .package-cards-item1 {
    width: 67%;
    margin: 0 auto;
  }

  .business-package-background {
    height: 1430px;
    background: url(/staticfiles/hughesnet-business-page-package-card-mobile.webp);
  }

  .internet-card-heading span {
    font-size: 30px;
  }

  .internet-card-heading span img {
    max-width: 100%;
    transform: unset;
  }

  .internet-card-heading {
    column-gap: 20px;
  }

  .services-availability {
    padding: 0px 0px;
  }

  .business-package-background {
    padding-top: 70px;
  }

  .online-fast-section.hughsnet-internet-plans {
    display: none;
  }

  .at-a-glance.lot-more-value.internet-avilable-my-area {
    margin-top: 0;
  }

  .at-a-glance.lot-more-value.internet-avilable-my-area .at-all-glace {
    background: url(/staticfiles/hughsnet-business-page-internet-services-my-area-image.-mobile-responsive.webp);
    height: 887px;
  }

  .at-a-glance.lot-more-value.internet-avilable-my-area .at-all-glace .glace-1 {
    justify-content: start;
  }

  .main-button.giving-your-more-freedom {
    justify-content: center;
    margin-top: 10px;
  }

  .hughsenet-heading p {
    line-height: 20px;
  }
}

@media only screen and (max-width: 389px) {
  .business-banner-section .hughsenet-heading h1 {
    font-size: 23px;
    line-height: 29px;
  }
  .all-main p {
    font-size: 13px;
    text-align: justify;
  }
  .business-banner-section .hughsenet-section {
    height: 100%;
  }
}
