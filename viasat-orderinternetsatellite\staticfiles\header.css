body {
  margin: 0;
}

.viasar-header {
  background: #202e39;
}

.logo {
  margin-top: 7px;
}

.navbar {
  display: flex;
  align-items: center;
  padding: 10px 12px 10px 49px;
  margin: 0 auto;
  justify-content: space-between;
  margin-bottom: -2px;
}

#menu {
  display: flex;
  gap: 25px;
  align-items: center;
}

.menu a {
  text-decoration: none;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  font-family: "SourceSansPro-Regular";
}

.hire-developer-btn .hire-dev {
  color: #fff;
  font-size: 14px;
  width: 180px;
  height: 36px;
  font-weight: 700;
  background: #677a89;
  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 8px;
}

.menu.hire-developer-btn {
  display: flex;
  column-gap: 14px;
  align-items: center;
}

.header-area {
  background: #202e39;
  text-align: center;
  position: relative;
  transition: background-color 0.3s;
}

header.sticky {
  position: fixed;
  top: 0;
  width: 100%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  background: #202e39;
  z-index: 99999;
}

.site-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 8px 20px 8px;
}

a.site-logo {
  font-size: 26px;
  font-weight: 800;
  text-transform: uppercase;
  color: #fff;
  text-decoration: none;
}

a.site-logo img {
  width: 100%;
}

.site-navbar ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.site-navbar ul li a {
  padding: 20px;
  display: block;
  font-weight: 400;
  text-decoration: none;
  font-size: 14px;
}


.site-navbar ul .lets-connecet {
  display: flex;
  align-items: center;
  padding-left: 20px;
}

.site-navbar ul .lets-connecet a {
  font-size: 14px;
  padding: 10px 20px;
  background-color: #0AC246;
  border-radius: 20px;
  color: #ffffff;
  font-weight: 400;
}

ul.sub-menu {
  position: absolute;
  z-index: 1;
  flex-direction: column;
  background: #fff;
  padding: 22px 18px 0px;
  box-shadow: 0 0 20px rgba(0, 0, 0, .5);
  display: none;
  transition: all 0.5s;
}

.services-menue:hover .sub-menu {
  display: block;
  transition: all 0.5s;
}

.site-navbar .sub-menu li a {
  padding: 0px;
  text-align: start;
}

/* navbar regular css end */


/* nav-toggler css start */
.nav-toggler {
  border: 3px solid #fff;
  padding: 5px;
  background-color: transparent;
  cursor: pointer;
  height: 39px;
  display: none;
}

.site-navbar .sub-menu li {
  padding-bottom: 20px;
}

.nav-toggler span,
.nav-toggler span:before,
.nav-toggler span:after {
  width: 28px;
  height: 3px;
  background-color: #fff;
  display: block;
  transition: .3s;
}

.nav-toggler span:before {
  content: '';
  transform: translateY(-9px);
}

.nav-toggler span:after {
  content: '';
  transform: translateY(6px);
}

.nav-toggler.toggler-open span {
  background-color: transparent;
}

.nav-toggler.toggler-open span:before {
  transform: translateY(0px) rotate(45deg);
}

.nav-toggler.toggler-open span:after {
  transform: translateY(-3px) rotate(-45deg);
}

a.site-logo img {
  width: 100%;
  max-width: 60%;
  transform: translate(0px, 6px);
}

/* nav-toggler css start */

@media screen and (max-width: 839px) {

  /* navbar css for mobile start */
  .nav-toggler {
    display: block;
  }

  .nav-toggler span,
  .nav-toggler span:before,
  .nav-toggler span:after {
    color: #0AC246;
  }

  .nav-toggler span,
  .nav-toggler span:before,
  .nav-toggler span:after {
    background-color: #fff;
  }

  .site-navbar {
    min-height: 60px;
    padding: 8px 20px 8px;
  }

  .site-navbar ul {
    position: absolute;
    width: 100%;
    height: calc(100vh - 60px);
    left: 0;
    top: 77px;
    flex-direction: column;
    align-items: center;
    background-color: rgba(0, 0, 0, .75);
    max-height: 0;
    overflow: hidden;
    transition: .3s;
  }

  .site-navbar ul li {
    width: 100%;
    text-align: center;
  }

  .site-navbar ul li a {
    padding: 25px;
    color: #ffffff;
  }

  .site-navbar ul li a:hover {
    background-color: rgba(255, 255, 255, .1);
  }

  .site-navbar ul.open {
    max-height: 61vh;
    overflow: visible;
    z-index: 9;
    transition: .3s;
  }

  .site-navbar ul .lets-connecet {
    justify-content: center;
  }

  a.site-logo img {
    width: 60%;
    transform: translate(0px, 5px);
  }
  .mobile-big-logo{
    display: none;
  }
}
@media screen and (max-width: 380px){
  .menu.hire-developer-btn{
    display: none;
  }
}