.hughsenet-section {
  display: flex;
  max-width: 1130px;
  margin: 0 auto;
  height: 100%;
  align-items: center;
}

.hughsenet-section .hughsenet-heading {
  width: 50%;
}

.hughsenet-heading h1 {
  color: #000;
  font-size: 40px;
  font-weight: 700;
  line-height: 50px;
  max-width: 620px;

  font-weight: 300;
}

.hughsenet-heading label {
  color: var(--color-secondary);
  font-size: 30px;
}

.hughsenet-heading span {
  color: #ff5820;
}

.hughsenet-heading-list {
  margin-top: 13px;
}

.hughsenet-heading-list h3 {
  display: flex;
  column-gap: 9px;
  padding-bottom: 8px;
}

.hughsenet-heading-list h3 span {
  color: #000;
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
}

.hughsenet-heading-list h3 span img {
  transform: translate(0px, 2px);
}

.business-banner-section .hughsenet-heading p {
  margin-top: 5px;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  font-family: var(--font-family-regular);
}

.main-button {
  display: flex;
  column-gap: 28px;
}

.main-button a {
  display: flex;
  border-radius: 10px;
  background: #0091c4;
  color: #fff;
  font-size: 20px;
  font-weight: 700;
  line-height: 20px;
  padding: 13px 16px;
  align-items: center;
  column-gap: 8px;
  margin-top: 40px;
}

.main-button a img {
  width: unset;
}

.main-button .call-icon {
  background-color: #ff5820;
}

.business-banner-section .hughsenet-heading span {
  color: var(--black);
}

.business-banner-section .banner-background {
  height: 672px;
  background: url(/staticfiles/viasait-businesspage-banner-image.webp);
  background-repeat: no-repeat;
  padding-left: 0;
  background-size: cover;
}

.business-banner-section .hughsenet-section {
  justify-content: space-between;
}

.business-banner-section .hughsenet-heading1 {
  max-width: 31.4%;
}

.top-bar {
  background-color: var(--color-secondary);
  font-weight: 500;
  color: #fff;
  font-size: 24px;
  line-height: 45px;
  padding: 15px 0px;
  display: flex;
  align-items: center;
  column-gap: 16px;
  justify-content: center;
}

.top-bar img {
  width: unset;
}

.form-container {
  padding: 20px;
  background-color: #fff;
}

.form-field {
  margin-bottom: 10px;
}

.form-field input {
  width: 96%;
  padding: 13px 0px;
  border-radius: 3px;
  border: 0.5px solid #8d8d8d;
  background: #f9f9f9;
  color: #000;
  font-size: 12px;
  font-weight: 300;
  line-height: 18px;
  padding-left: 16px;
  font-family: var(--font-family-light);
}

.submit-button {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  width: 100%;
  padding: 13px 0px;
  background-color: var(--color-secondary);
  border: none;
  cursor: pointer;
  font-family: var(--font-family-regular);
}

.form-container p {
  color: #000;
  text-align: justify;
  font-size: 12px;
  font-weight: 300;
  line-height: 15px;
  font-family: var(--font-family-light);
  margin-top: 26px;
}

.business-package-background {
  background-image: url(/staticfiles/viasat-businesspage-business-page-package-card.webp);
  padding-top: 98px;
  padding-bottom: 39px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: bottom;
}

.package-cards-items {
  display: flex;
  max-width: 1024px;
  margin: 0 auto;
  justify-content: space-between;
}

.package-cards-item1 {
  background: #fff;
  box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.1);
  padding: 21px;
  width: 26%;
}

.business-banner-section .top-bar a {
  color: var(--white);
  font-family: var(--font-family-primary);
  font-size: 20px;
  font-weight: 400;
}

.internet-card-heading {
  display: flex;
  align-items: center;
  column-gap: 20px;
  margin-bottom: 22px;
}

.internet-card-heading span {
  font-size: 30px;
  font-weight: 500;
  line-height: 30px;
  color: var(--color-secondary);
  font-family: var(--font-family-primary);
}

.package-cards-item1 p,
.package-cards-item1 ul li {
  color: #000;
  font-size: 15px;
  font-style: normal;
  font-weight: 300;
  font-family: var(--font-family-light);
  line-height: 20px;
}

.cards-paragraph p {
  color: #fff;
  text-align: justify;
  font-size: 14px;
  line-height: 25px;
  margin: 0 auto;
  margin-top: 70px;
  max-width: 1252px;
  padding: 0 20px;
  font-family: var(--font-family-light);
}

.home-install-business .rulal-area-card {
  background-color: var(--color-link);
}

.home-install-business .what-viasat-order a {
  background-color: var(--color-primary);
}
.what-viasat.rulal-area.home-install-business {
  margin-bottom: -4px;
}
@media only screen and (max-width: 1250px) {
  .hughsenet-section {
    max-width: 965px;
  }

  .package-cards-items {
    max-width: 940px;
  }

  .cards-paragraph p {
    max-width: 1025px;
  }
}

@media only screen and (max-width: 1000px) {
  .hughsenet-section {
    max-width: 840px;
    padding: 0 20px;
  }

  .business-banner-section .hughsenet-heading1 {
    max-width: 42.4%;
  }

  .business-banner-section .banner-background {
    background-position: 50%;
  }

  .hughsenet-heading h1 {
    font-size: 30px;
    line-height: 41px;
  }

  .package-cards-items {
    max-width: 800px;
  }

  .package-cards-item1 p,
  .package-cards-item1 ul li {
    font-size: 13px;
  }

  .cards-paragraph p {
    max-width: 850px;
  }
}

@media only screen and (max-width: 869px) {
  .business-banner-section .hughsenet-section {
    flex-direction: column;
    row-gap: 30px;
  }

  .hughsenet-section .hughsenet-heading {
    width: 100%;
  }

  .business-banner-section .hughsenet-heading1 {
    max-width: 100%;
  }

  .business-banner-section .banner-background {
    height: unset;
    padding: 40px 0px;
    background-position: 0;
  }

  .business-banner-section .banner-headings {
    max-width: unset;
  }
}

@media only screen and (max-width: 767px) {
  .business-banner-section .banner-background {
    background: url(/staticfiles/viasait-business-banner-background-mobile.webp);
    height: 1189px;
    padding: 0px;
  }

  .business-banner-section .hughsenet-section {
    flex-direction: column;
    height: unset;
    row-gap: 72px;
    padding-top: 40px;
  }

  .business-banner-section .hughsenet-section .hughsenet-heading,
  .business-banner-section .hughsenet-heading1 {
    width: 100%;
    max-width: 100%;
  }

  .package-cards-items {
    flex-direction: column;
    row-gap: 20px;
  }

  .package-cards-item1 {
    width: 67%;
    margin: 0 auto;
  }

  .business-package-background {
    height: 1691px;
    background: url(/staticfiles/viasat-internet-voice-video-mobile-image.webp);
  }

  .internet-card-heading span {
    font-size: 30px;
  }

  .internet-card-heading span img {
    max-width: 100%;
    transform: unset;
  }

  .internet-card-heading {
    column-gap: 20px;
  }

  .cards-paragraph p {
    margin-top: 210px;
    font-size: 11px;
    line-height: 20px;
  }

  .business-package-background {
    padding-top: 70px;
  }

  .online-fast-section.hughsnet-internet-plans {
    display: none;
  }

  .at-a-glance.lot-more-value.internet-avilable-my-area {
    margin-top: 0;
  }

  .at-a-glance.lot-more-value.internet-avilable-my-area .at-all-glace {
    background: url(/staticfiles/hughsnet-business-page-internet-services-my-area-image.-mobile-responsive.webp);
    height: 887px;
  }

  .at-a-glance.lot-more-value.internet-avilable-my-area .at-all-glace .glace-1 {
    justify-content: start;
  }

  .main-button.giving-your-more-freedom {
    justify-content: center;
    margin-top: 10px;
  }
  .what-viasat.rulal-area {
    padding: 0;
  }
}
