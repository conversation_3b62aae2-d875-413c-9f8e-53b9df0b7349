<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="../staticfiles/business.css">
    <link href="https:/staticfiles/.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
</head>

<body>
    <main class="all-main">
        <!-- banner-section -->
        <section class="banner-section business-banner-section">
            <div class="banner-background">
                <div class="hughsenet-section">
                    <div class="hughsenet-heading">
                        <h1>Find the Perfect <span>Business Internet Plans</span></h1>
                        <p>We work with multiple service providers, checking services, identifying pricing and
                            promotions, and communicating changing dates. Save time and avoid the frustration of dealing
                            with multiple service providers. InternetOffersNow’s expert solutions advisors will handle
                            everything for you, from checking service availability to identifying the best pricing and
                            promotions.This means you can focus on what really matters - growing your business. Whether
                            it’s basic business bundles, dedicated bandwidth, or enterprise-level service, let us handle
                            the hassle so you can easily achieve success. We work with multiple service providers,
                            checking serviceability, identifying pricing and promotions, and communicating changing
                            dates. This process takes a lot of time and can become frustrating and confusing.
                        </p>
                    </div>
                    <div class="hughsenet-heading1">
                        <div class="top-bar">
                            <img src="../staticfiles/hughsnet-business-page-call-icon.webp" alt="Call Icon">
                            Call Now (888)-873-0351
                        </div>
                        <div class="form-container">
                            <form>
                                <div class="form-field">
                                    <input type="text" id="name" name="name" placeholder="Name" required>
                                </div>
                                <div class="form-field">
                                    <input type="text" id="businessName" name="businessName" placeholder="Business Name"
                                        required>
                                </div>
                                <div class="form-field">
                                    <input type="text" id="businessAddress" name="businessAddress"
                                        placeholder="Business Address" required>
                                </div>
                                <div class="form-field">
                                    <input type="tel" id="phone" name="phone" placeholder="Phone Number" required>
                                </div>
                                <div class="form-field">
                                    <input type="email" id="email" name="email" placeholder="Email Address" required>
                                </div>
                                <button type="submit" class="submit-button">Request a Quote</button>
                                <p>By choosing submit, I provide my signature, expressly consenting to contact from
                                    ClearConnect.com, or its subsidiaries, at the number provided regarding its products
                                    or customer service via live, prerecorded telephone call, or email message. I
                                    understand that my telephone company may impose charges on me for these contacts and
                                    I am not required to sign this document as a condition to purchase any good or
                                    service. I understand that I can revoke this consent at any time.</p>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- cards-section -->

        <section class="business-package-cards">
            <div class="business-package-background">
                <div class="package-cards-items">
                    <div class="package-cards-item1">
                        <div class="internet-card-heading">
                            <span><img src="../staticfiles/hugsnet-business-page-package-card-icon.webp"
                                    alt=""></span>
                            <span>Internet</span>
                        </div>
                        <p>Stay connected and productive with ease! Our team of experts will analyze your business needs
                            and find the perfect network solution for you. Best of all, we'll deliver the right solution
                            with the right carrier at the right price, giving you peace of mind and cost savings. Focus
                            on your business, and let us handle the rest! Focus on your business, and let us handle the
                            rest!</p>
                        <div class="inter-package-list">
                            <ul>
                                <li>Ethernet</li>
                                <li>Fiber</li>
                                <li>Cable</li>
                                <li>Satellite</li>
                                <li>Redundancy</li>
                            </ul>
                        </div>
                    </div>
                    <div class="package-cards-item1">
                        <div class="internet-card-heading">
                            <span><img src="../staticfiles/hugsnet-business-page-package-card-icon1.webp"
                                    alt=""></span>
                            <span>Voice</span>
                        </div>
                        <p>Our business specialists can help you connect with the voice solution that was created for
                            your company and discover the best fit at the best price.Best of all, we'll deliver the
                            right solution with the right carrier at the right price, giving you peace of mind. with the
                            right carrier at the right price, </p>
                        <div class="inter-package-list">
                            <ul>
                                <li>Hosted VoIP</li>
                                <li>UCaaS</li>
                                <li>Local</li>
                                <li>SIP</li>
                                <li>PRI</li>
                                <li>Unified</li>
                                <li>Communications</li>
                            </ul>
                        </div>
                    </div>
                    <div class="package-cards-item1">
                        <div class="internet-card-heading">
                            <span><img src="../staticfiles/hugsnet-business-page-package-card-icon2.webp"
                                    alt=""></span>
                            <span>Video</span>
                        </div>
                        <p>Give your business the competitive edge it deserves by creating a tailored ambiance with our
                            customizable channel lineup. With the freedom to choose from a great selection of channels,
                            you can create a welcoming atmosphere for your customers while providing your employees with
                            programming that helps them excel at their job. Our business experts will help you pick the
                            perfect package for your business needs. Our business experts will help you pick the perfect
                            package for your business needs. Our business experts will help you pick the </p>
                    </div>
                </div>
                <div class="cards-paragraph">
                    <p>Terms and ConditionsRestrictions apply. Services not available in all areas. Featured bundles are
                        available nationwide where services are available. Minimum term contract required. Early
                        termination fees apply. Any Comcast-provided equipment must be returned in good working order.
                        Equipment, installation, and taxes extra and subject to change. Business Internet: Connection
                        type, device capabilities and other factors affect speed. Actual speeds vary and are not
                        guaranteed. Voice Mobility Lines: Service (including 911/emergency services) may not function
                        after an extended power outage. Transfers of existing telephone number not always available.</p>
                </div>
            </div>
        </section>

        <!--blue banner-->
        <section class="online-fast-section hughsnet-internet-plans">
            <div class="online-fast-image">
                <div class="all-online-fast">
                    <div class="online-fast-heading">
                        <h2>HughesNet Internet Plan for</br>Your Home & Save $60 Instantly*!</h2>
                    </div>
                    <div class="online-fast-heading-cta">
                        <div class="arrow-container">
                            <img src="../staticfiles/hughesnet-home-online-fast-image-arrow.webp" alt="Arrow"
                                class="arrow">
                        </div>
                        <div class="all-online-buttn">
                            <a href=tel:8888730351><img src="../staticfiles/hughsnet-home-page-banner-phone-icon.webp"
                                    alt="">************</a>
                        </div>
                    </div>
                </div>
                <div class="free-paragraph">
                    <p>*Free standard professional installation applies to new Lease subscribers only. Not valid
                        with Purchase option. Limited-time offer.</p>
                </div>
            </div>
        </section>
        
        <section class="at-a-glance lot-more-value internet-avilable-my-area">
            <div class="at-all-glace">
                <div class="glace-1">
                    <div class="glace-heading">
                        <h2>“Are You Sure <span>HughesNet®</span> Internet Service Is Available in My Area?”!</h2>
                        <div class="inter-sufring-listing my-area-services">
                            <p>HughesNet has a well-established history of serving America’s government, military,
                                businesses and residential neighborhoods since 1996―although it was not until 2012 the
                                brand was given its current name. Between 2016 and 2018, network coverage expanded―with
                                Brazil, Colombia, Peru and Ecuador coming into the Hughes fold.</p>
                            <p>Today, HughesNet coverage comes to the rescue of all Americans who have their homes away
                                from the hustle-bustle of urban life. In areas where there are little to no wired
                                high-speed options, HughesNet satellite internet is your best bet.</p>
                            <p>To find out about the exact HughesNet package offers available in your area, call at
                                ************ .</p>
                        </div>
                        <div class="main-button giving-your-more-freedom">
                            <a href=tel:8888730351 class="dollar-icon"><img
                                    src="../staticfiles/hughsnet-home-page-banner-glance-phone-icon.webp" alt="">Call
                                to Order</a>
                        </div>
                    </div>
                    <div class="glace-heading-image">
                    </div>
                </div>
            </div>

        </section>
    </main>

    <script>
        const items = document.querySelectorAll('.accordion button');

        function toggleAccordion() {
            const itemToggle = this.getAttribute('aria-expanded');

            for (i = 0; i < items.length; i++) {
                items[i].setAttribute('aria-expanded', 'false');
            }

            if (itemToggle == 'false') {
                this.setAttribute('aria-expanded', 'false');
            }
        }
        items.forEach((item) => item.addEventListener('click', toggleAccordion));




    </script>
</body>

</html>