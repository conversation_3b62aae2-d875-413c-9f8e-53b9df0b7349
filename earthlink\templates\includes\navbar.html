<main class="all-main">
  <header class="all-header">
    <nav class="flex-navbar">
      <a href="/" class="unlimeted-logo">
        <div class="logo">
          <img src="/staticfiles/images/ultimateinternetplans-logo.svg" alt="" />
        </div>
      </a>
      <ul class="nav-links">
        <li><a href="/">HOME</a></li>
        <li><a href="/contact-us/">CONTACT</a></li>
      </ul>
      <div class="el-ar-live">
        <a href="tel:8555179889"
          ><img src="/staticfiles/images/el-footer-call.svg" alt="" />************</a
        >
      </div>
      <div class="burger-button">
        <div class="line1"></div>
        <div class="line2"></div>
        <div class="line3"></div>
      </div>
    </nav>
  </header>
  <script>
    "use strict";

    const navSlide = function () {
      const burger = document.querySelector(".burger-button");
      const nav = document.querySelector(".nav-links");
      const navLinks = document.querySelectorAll(".nav-links li");

      // When click to the burger button
      burger.addEventListener("click", () => {
        // Toggle NavBar
        nav.classList.toggle("nav-active");

        // NavBar Links Animation
        navLinks.forEach((link, index) => {
          if (link.style.animation) link.style.animation = "";
          else link.style.animation = "navLinksFade 0.5s ease forwards " + (index / 7 + 0.3) + "s";
        });

        //Burger Animation
        burger.classList.toggle("toggle-burger");
      });
    };

    navSlide();
  </script>
</main>
